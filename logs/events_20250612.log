2025/06/12 00:49:08 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 00:49:08 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 00:49:08 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 00:49:08 main.go:195: API接口:
2025/06/12 00:49:08 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 00:49:08 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 00:49:08 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 00:49:08 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 00:49:08 main.go:200: 期权系统API接口:
2025/06/12 00:49:08 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 00:49:08 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 00:49:08 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 00:49:08 main.go:389: 
2025/06/12 00:49:08 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 00:49:08 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 00:49:08 main.go:393:       示例: add long 3500 0.0005
2025/06/12 00:49:08 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 00:49:08 main.go:395: 
2025/06/12 00:49:08 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 00:49:08 main.go:397:    list                                     - 列出所有任务
2025/06/12 00:49:08 main.go:398: 
2025/06/12 00:49:08 main.go:399: ⚙️ 参数配置:
2025/06/12 00:49:08 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 00:49:08 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 00:49:08 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 00:49:08 main.go:403: 
2025/06/12 00:49:08 main.go:404: 📊 状态控制:
2025/06/12 00:49:08 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 00:49:08 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 00:49:08 main.go:407:    status off                               - 停止状态输出
2025/06/12 00:49:08 main.go:408: 
2025/06/12 00:49:08 main.go:409: 📋 数据导出:
2025/06/12 00:49:08 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 00:49:08 main.go:411: 
2025/06/12 00:49:08 main.go:412: 💾 数据管理:
2025/06/12 00:49:08 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 00:49:08 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 00:49:08 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 00:49:08 main.go:416: 
2025/06/12 00:49:08 main.go:417: 📋 事件查看:
2025/06/12 00:49:08 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 00:49:08 main.go:419: 
2025/06/12 00:49:08 main.go:420: 🎛️  批量控制:
2025/06/12 00:49:08 api.go:35: API服务器启动，端口: 5879
2025/06/12 00:49:08 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 00:49:08 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 00:49:08 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 00:49:08 main.go:424: 
2025/06/12 00:49:08 main.go:425: 🗑️  任务管理:
2025/06/12 00:49:08 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 00:49:08 main.go:427: 
2025/06/12 00:49:08 main.go:428: 🔧 其他:
2025/06/12 00:49:08 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 00:49:08 main.go:430:    quit | exit                              - 退出程序
2025/06/12 00:49:08 main.go:431: ========================
2025/06/12 00:49:08 main.go:432: 
2025/06/12 00:49:26 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 00:49:26 main.go:728: 
2025/06/12 00:49:26 main.go:827: M945000 停止   L    2800 0.020% 0.100  无仓位                   2  14:01  11h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/12 00:49:26 main.go:827: M266000 停止   L    2750 0.020% 0.100  无仓位                   1  17:40   7h   0.00    16.04   0.06 是   options  ETH-12JUN25-2750-C-...
2025/06/12 00:49:26 main.go:827: M207000 停止   L    2750 0.020% 0.100  无仓位                   1  13:30  11h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/12 00:49:26 main.go:827: M541000 停止   S    2750 0.020% 0.100  无仓位                   0  13:30  11h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/12 00:49:26 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 00:50:03 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 00:50:03 main.go:225: 停止任务 M945000 失败: 任务已停止: M945000
2025/06/12 00:50:03 main.go:225: 停止任务 M207000 失败: 任务已停止: M207000
2025/06/12 00:50:03 main.go:225: 停止任务 M266000 失败: 任务已停止: M266000
2025/06/12 00:50:03 main.go:225: 停止任务 M541000 失败: 任务已停止: M541000
2025/06/12 00:50:22 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 00:50:22 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 00:50:22 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 00:50:22 main.go:195: API接口:
2025/06/12 00:50:22 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 00:50:22 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 00:50:22 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 00:50:22 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 00:50:22 main.go:200: 期权系统API接口:
2025/06/12 00:50:22 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 00:50:22 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 00:50:22 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 00:50:22 main.go:389: 
2025/06/12 00:50:22 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 00:50:22 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 00:50:22 main.go:393:       示例: add long 3500 0.0005
2025/06/12 00:50:22 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 00:50:22 main.go:395: 
2025/06/12 00:50:22 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 00:50:22 main.go:397:    list                                     - 列出所有任务
2025/06/12 00:50:22 main.go:398: 
2025/06/12 00:50:22 main.go:399: ⚙️ 参数配置:
2025/06/12 00:50:22 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 00:50:22 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 00:50:22 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 00:50:22 main.go:403: 
2025/06/12 00:50:22 main.go:404: 📊 状态控制:
2025/06/12 00:50:22 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 00:50:22 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 00:50:22 main.go:407:    status off                               - 停止状态输出
2025/06/12 00:50:22 main.go:408: 
2025/06/12 00:50:22 main.go:409: 📋 数据导出:
2025/06/12 00:50:22 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 00:50:22 main.go:411: 
2025/06/12 00:50:22 main.go:412: 💾 数据管理:
2025/06/12 00:50:22 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 00:50:22 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 00:50:22 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 00:50:22 main.go:416: 
2025/06/12 00:50:22 main.go:417: 📋 事件查看:
2025/06/12 00:50:22 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 00:50:22 main.go:419: 
2025/06/12 00:50:22 main.go:420: 🎛️  批量控制:
2025/06/12 00:50:22 api.go:35: API服务器启动，端口: 5879
2025/06/12 00:50:22 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 00:50:22 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 00:50:22 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 00:50:22 main.go:424: 
2025/06/12 00:50:22 main.go:425: 🗑️  任务管理:
2025/06/12 00:50:22 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 00:50:22 main.go:427: 
2025/06/12 00:50:22 main.go:428: 🔧 其他:
2025/06/12 00:50:22 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 00:50:22 main.go:430:    quit | exit                              - 退出程序
2025/06/12 00:50:22 main.go:431: ========================
2025/06/12 00:50:22 main.go:432: 
2025/06/12 00:50:24 main.go:694: 📋 当前没有运行的任务
2025/06/12 00:51:54 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 00:51:54 状态变化:  -> running | 原因: 任务创建
2025/06/12 00:51:54 manager.go:94: 创建对冲任务: 2467000, 交易对: ETHUSDT, 方向: long, 目标价: 2865, 来源: manual
2025/06/12 00:51:54 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2865.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 00:51:54 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 00:51:54 hedge_task.go:45: 🚀 启动对冲任务: 2467000 (新版本)
2025/06/12 00:51:54 hedge_task.go:480: 📊 任务 2467000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 00:51:54 orderbook_manager.go:67: 📈 任务 2467000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 00:51:54 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 00:51:54 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 00:51:54 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 00:51:55 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:51:55 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:51:55 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:55 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:55 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:55 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:55 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:56 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521634672, 模式: POST_ONLY
2025/06/12 00:51:57 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:51:57 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:57 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521636135, 模式: POST_ONLY
2025/06/12 00:51:58 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:51:58 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:58 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521640198, 模式: POST_ONLY
2025/06/12 00:51:59 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:51:59 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:51:59 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521643776, 模式: POST_ONLY
2025/06/12 00:52:00 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:52:00 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:00 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521647462, 模式: POST_ONLY
2025/06/12 00:52:01 hedge_task.go:595: 🎯 任务 2467000: 开始仓位调整 - 现有: none 0, 应有: long 0.01
2025/06/12 00:52:01 hedge_task.go:641: 📈 任务 2467000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2865.573, 触发源: price_trigger
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:01 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:02 hedge_task.go:586: ⚠️  任务 2467000: 已有下单在进行中，忽略本次调整
2025/06/12 00:52:02 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2865.57, 订单ID: 8389765903521654342, 模式: POST_ONLY
2025/06/12 00:52:02 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 00:52:02 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 00:52:02 manager.go:141: 停止对冲任务: 2467000
2025/06/12 00:52:02 hedge_task.go:69: 📢 任务 2467000: 收到停止信号
2025/06/12 00:52:02 orderbook_manager.go:104: 📉 任务 2467000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 00:52:02 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 00:52:02 hedge_task.go:71: 🛑 对冲任务结束: 2467000
2025/06/12 00:52:02 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 00:52:02 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 00:52:02 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:16:42 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 10:16:42 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 10:16:42 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 10:16:42 main.go:195: API接口:
2025/06/12 10:16:42 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 10:16:42 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 10:16:42 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 10:16:42 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 10:16:42 main.go:200: 期权系统API接口:
2025/06/12 10:16:42 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 10:16:42 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 10:16:42 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 10:16:42 main.go:389: 
2025/06/12 10:16:42 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 10:16:42 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 10:16:42 main.go:393:       示例: add long 3500 0.0005
2025/06/12 10:16:42 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 10:16:42 main.go:395: 
2025/06/12 10:16:42 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 10:16:42 main.go:397:    list                                     - 列出所有任务
2025/06/12 10:16:42 main.go:398: 
2025/06/12 10:16:42 main.go:399: ⚙️ 参数配置:
2025/06/12 10:16:42 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 10:16:42 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 10:16:42 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 10:16:42 main.go:403: 
2025/06/12 10:16:42 main.go:404: 📊 状态控制:
2025/06/12 10:16:42 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 10:16:42 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 10:16:42 main.go:407:    status off                               - 停止状态输出
2025/06/12 10:16:42 main.go:408: 
2025/06/12 10:16:42 main.go:409: 📋 数据导出:
2025/06/12 10:16:42 api.go:35: API服务器启动，端口: 5879
2025/06/12 10:16:42 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 10:16:42 main.go:411: 
2025/06/12 10:16:42 main.go:412: 💾 数据管理:
2025/06/12 10:16:42 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 10:16:42 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 10:16:42 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 10:16:42 main.go:416: 
2025/06/12 10:16:42 main.go:417: 📋 事件查看:
2025/06/12 10:16:42 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 10:16:42 main.go:419: 
2025/06/12 10:16:42 main.go:420: 🎛️  批量控制:
2025/06/12 10:16:42 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 10:16:42 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 10:16:42 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 10:16:42 main.go:424: 
2025/06/12 10:16:42 main.go:425: 🗑️  任务管理:
2025/06/12 10:16:42 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 10:16:42 main.go:427: 
2025/06/12 10:16:42 main.go:428: 🔧 其他:
2025/06/12 10:16:42 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 10:16:42 main.go:430:    quit | exit                              - 退出程序
2025/06/12 10:16:42 main.go:431: ========================
2025/06/12 10:16:42 main.go:432: 
2025/06/12 10:16:45 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 10:16:45 main.go:728: 
2025/06/12 10:16:45 main.go:827: 2467000 停止   L    2865 0.020% 0.010  无仓位                   0  00:51   9h   0.00     0.00   0.00 是   manual   -                     
2025/06/12 10:16:45 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 10:17:52 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 10:17:52 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/12 10:17:52 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:18:03 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 10:18:03 状态变化:  -> running | 原因: 任务创建
2025/06/12 10:18:03 manager.go:94: 创建对冲任务: 5312000, 交易对: ETHUSDT, 方向: long, 目标价: 2770, 来源: manual
2025/06/12 10:18:03 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2770.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 10:18:03 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 10:18:03 hedge_task.go:45: 🚀 启动对冲任务: 5312000 (新版本)
2025/06/12 10:18:03 hedge_task.go:479: 📊 任务 5312000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 10:18:03 orderbook_manager.go:67: 📈 任务 5312000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 10:18:03 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:18:03 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:18:03 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:18:06 binance_client.go:232: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/12 10:18:12 hedge_task.go:612: 🎯 任务 5312000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:18:12 hedge_task.go:643: 🚀 任务 5312000: 订单执行器启动
2025/06/12 10:18:12 hedge_task.go:669: 📈 任务 5312000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2770.554, 触发源: price_trigger
2025/06/12 10:18:13 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.55, 订单ID: 8389765903750455613, 模式: POST_ONLY
2025/06/12 10:18:13 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:13 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903750455613
2025/06/12 10:18:13 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 重新下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history，查询订单状态
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:14 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:14 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:14 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:14 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:14 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:14 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:15 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:15 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:15 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:15 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:15 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:15 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:16 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:16 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:16 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:16 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:16 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:16 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:16 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:16 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:16 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:16 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:16 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:16 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:17 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:17 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:17 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:17 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:17 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:17 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:17 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:17 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:18 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:18 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:18 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:18 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:18 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:18 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:18:18 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:18 hedge_task.go:497: ⚠️ 任务 5312000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:18:18 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 10:18:18 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 10:18:18 manager.go:141: 停止对冲任务: 5312000
2025/06/12 10:18:18 hedge_task.go:69: 📢 任务 5312000: 收到停止信号
2025/06/12 10:18:18 orderbook_manager.go:104: 📉 任务 5312000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 10:18:18 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:18:18 hedge_task.go:71: 🛑 对冲任务结束: 5312000
2025/06/12 10:18:18 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 10:18:18 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:18:18 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:18:19 hedge_task.go:948: 📊 任务 5312000: 订单状态查询 - 订单ID: 8389765903750455613, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:18:19 hedge_task.go:964: 🚫 任务 5312000: 订单已被取消
2025/06/12 10:18:19 hedge_task.go:847: 📊 任务 5312000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.59
2025/06/12 10:18:19 hedge_task.go:878: ❌ 任务 5312000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:34:23 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 10:34:23 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 10:34:23 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 10:34:23 main.go:195: API接口:
2025/06/12 10:34:23 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 10:34:23 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 10:34:23 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 10:34:23 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 10:34:23 main.go:200: 期权系统API接口:
2025/06/12 10:34:23 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 10:34:23 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 10:34:23 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 10:34:23 main.go:389: 
2025/06/12 10:34:23 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 10:34:23 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 10:34:23 main.go:393:       示例: add long 3500 0.0005
2025/06/12 10:34:23 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 10:34:23 main.go:395: 
2025/06/12 10:34:23 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 10:34:23 main.go:397:    list                                     - 列出所有任务
2025/06/12 10:34:23 main.go:398: 
2025/06/12 10:34:23 main.go:399: ⚙️ 参数配置:
2025/06/12 10:34:23 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 10:34:23 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 10:34:23 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 10:34:23 main.go:403: 
2025/06/12 10:34:23 main.go:404: 📊 状态控制:
2025/06/12 10:34:23 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 10:34:23 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 10:34:23 main.go:407:    status off                               - 停止状态输出
2025/06/12 10:34:23 main.go:408: 
2025/06/12 10:34:23 main.go:409: 📋 数据导出:
2025/06/12 10:34:23 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 10:34:23 main.go:411: 
2025/06/12 10:34:23 main.go:412: 💾 数据管理:
2025/06/12 10:34:23 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 10:34:23 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 10:34:23 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 10:34:23 main.go:416: 
2025/06/12 10:34:23 main.go:417: 📋 事件查看:
2025/06/12 10:34:23 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 10:34:23 main.go:419: 
2025/06/12 10:34:23 main.go:420: 🎛️  批量控制:
2025/06/12 10:34:23 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 10:34:23 api.go:35: API服务器启动，端口: 5879
2025/06/12 10:34:23 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 10:34:23 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 10:34:23 main.go:424: 
2025/06/12 10:34:23 main.go:425: 🗑️  任务管理:
2025/06/12 10:34:23 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 10:34:23 main.go:427: 
2025/06/12 10:34:23 main.go:428: 🔧 其他:
2025/06/12 10:34:23 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 10:34:23 main.go:430:    quit | exit                              - 退出程序
2025/06/12 10:34:23 main.go:431: ========================
2025/06/12 10:34:23 main.go:432: 
2025/06/12 10:34:25 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 10:34:25 main.go:728: 
2025/06/12 10:34:25 main.go:827: 5312000 停止   L    2770 0.020% 0.010  无仓位                   0  10:18  16m   0.00     0.00   0.00 是   manual   -                     
2025/06/12 10:34:25 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 10:34:31 状态变化: stopped -> running | 原因: 批量启动
2025/06/12 10:34:31 hedge_task.go:45: 🚀 启动对冲任务: 5312000 (新版本)
2025/06/12 10:34:31 hedge_task.go:479: 📊 任务 5312000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 10:34:31 orderbook_manager.go:67: 📈 任务 5312000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 10:34:31 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:34:31 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:34:31 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:34:46 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 10:34:46 hedge_task.go:69: 📢 任务 5312000: 收到停止信号
2025/06/12 10:34:46 orderbook_manager.go:104: 📉 任务 5312000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 10:34:46 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:34:46 hedge_task.go:71: 🛑 对冲任务结束: 5312000
2025/06/12 10:34:46 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 10:34:46 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/12 10:34:46 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:34:59 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 10:34:59 状态变化:  -> running | 原因: 任务创建
2025/06/12 10:34:59 manager.go:94: 创建对冲任务: 7731000, 交易对: ETHUSDT, 方向: long, 目标价: 2770, 来源: manual
2025/06/12 10:34:59 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2770.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 10:34:59 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 10:34:59 hedge_task.go:45: 🚀 启动对冲任务: 7731000 (新版本)
2025/06/12 10:34:59 hedge_task.go:479: 📊 任务 7731000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 10:34:59 orderbook_manager.go:67: 📈 任务 7731000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 10:34:59 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:34:59 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:34:59 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 10:35:11 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:35:11 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:11 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2770.554, 触发源: price_trigger
2025/06/12 10:35:11 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.55, 订单ID: 8389765903756933981, 模式: POST_ONLY
2025/06/12 10:35:11 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2770.79
2025/06/12 10:35:12 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756933981
2025/06/12 10:35:12 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.79, 订单ID: 8389765903756934328, 模式: POST_ONLY
2025/06/12 10:35:12 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756933981, 新订单ID: 8389765903756934328, 新价格: 2770.79
2025/06/12 10:35:12 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.79, 新盘口: 2771
2025/06/12 10:35:12 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756934328
2025/06/12 10:35:12 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771, 订单ID: 8389765903756934670, 模式: POST_ONLY
2025/06/12 10:35:12 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756934328, 新订单ID: 8389765903756934670, 新价格: 2771
2025/06/12 10:35:12 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771, 新盘口: 2771.08
2025/06/12 10:35:13 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756934670
2025/06/12 10:35:13 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.08, 订单ID: 8389765903756942898, 模式: POST_ONLY
2025/06/12 10:35:13 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756934670, 新订单ID: 8389765903756942898, 新价格: 2771.08
2025/06/12 10:35:13 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.08, 新盘口: 2771.19
2025/06/12 10:35:13 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756942898
2025/06/12 10:35:14 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.19, 订单ID: 8389765903756947019, 模式: POST_ONLY
2025/06/12 10:35:14 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756942898, 新订单ID: 8389765903756947019, 新价格: 2771.19
2025/06/12 10:35:14 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.19, 新盘口: 2771.26
2025/06/12 10:35:14 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756947019
2025/06/12 10:35:14 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:14 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:14 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.26, 订单ID: 8389765903756949933, 模式: POST_ONLY
2025/06/12 10:35:14 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756947019, 新订单ID: 8389765903756949933, 新价格: 2771.26
2025/06/12 10:35:14 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.26, 新盘口: 2771.27
2025/06/12 10:35:14 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756949933
2025/06/12 10:35:14 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.27, 订单ID: 8389765903756953286, 模式: POST_ONLY
2025/06/12 10:35:14 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756949933, 新订单ID: 8389765903756953286, 新价格: 2771.27
2025/06/12 10:35:14 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.27, 新盘口: 2771.46
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:15 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756953286
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.46, 订单ID: 8389765903756961254, 模式: POST_ONLY
2025/06/12 10:35:16 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756953286, 新订单ID: 8389765903756961254, 新价格: 2771.46
2025/06/12 10:35:16 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:16 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756961254, 状态: NEW, 原因: timeout
2025/06/12 10:35:16 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.46, 新盘口: 2771.48
2025/06/12 10:35:16 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756961254
2025/06/12 10:35:17 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.48, 订单ID: 8389765903756961597, 模式: POST_ONLY
2025/06/12 10:35:17 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756961254, 新订单ID: 8389765903756961597, 新价格: 2771.48
2025/06/12 10:35:17 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:17 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756961597, 状态: NEW, 原因: timeout
2025/06/12 10:35:17 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.48, 新盘口: 2771.62
2025/06/12 10:35:17 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756961597
2025/06/12 10:35:17 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:17 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.62, 订单ID: 8389765903756965614, 模式: POST_ONLY
2025/06/12 10:35:17 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756961597, 新订单ID: 8389765903756965614, 新价格: 2771.62
2025/06/12 10:35:17 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.62, 新盘口: 2771.64
2025/06/12 10:35:17 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:17 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756965614
2025/06/12 10:35:17 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:18 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.64, 订单ID: 8389765903756965848, 模式: POST_ONLY
2025/06/12 10:35:18 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756965614, 新订单ID: 8389765903756965848, 新价格: 2771.64
2025/06/12 10:35:18 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:18 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:18 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756965848, 状态: NEW, 原因: timeout
2025/06/12 10:35:18 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.64, 新盘口: 2771.99
2025/06/12 10:35:18 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756965848
2025/06/12 10:35:18 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:18 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.99, 订单ID: 8389765903756968508, 模式: POST_ONLY
2025/06/12 10:35:18 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756965848, 新订单ID: 8389765903756968508, 新价格: 2771.99
2025/06/12 10:35:18 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.99, 新盘口: 2772.16
2025/06/12 10:35:19 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756968508
2025/06/12 10:35:19 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2772.16, 订单ID: 8389765903756974211, 模式: POST_ONLY
2025/06/12 10:35:19 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756968508, 新订单ID: 8389765903756974211, 新价格: 2772.16
2025/06/12 10:35:19 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:19 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756974211, 状态: NEW, 原因: timeout
2025/06/12 10:35:19 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2772.16, 新盘口: 2772.05
2025/06/12 10:35:19 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756974211
2025/06/12 10:35:19 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2772.05, 订单ID: 8389765903756976203, 模式: POST_ONLY
2025/06/12 10:35:19 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756974211, 新订单ID: 8389765903756976203, 新价格: 2772.05
2025/06/12 10:35:19 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2772.05, 新盘口: 2772.16
2025/06/12 10:35:20 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756976203
2025/06/12 10:35:20 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2772.16, 订单ID: 8389765903756977637, 模式: POST_ONLY
2025/06/12 10:35:20 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903756976203, 新订单ID: 8389765903756977637, 新价格: 2772.16
2025/06/12 10:35:20 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:20 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756977637, 状态: NEW, 原因: timeout
2025/06/12 10:35:20 hedge_task.go:719: ⏰ 任务 7731000: 订单超时，查询订单状态
2025/06/12 10:35:21 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756977637, 状态: NEW, 原因: timeout
2025/06/12 10:35:21 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2772.16, 新盘口: 2772.49
2025/06/12 10:35:21 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756977637
2025/06/12 10:35:21 hedge_task.go:886: ❌ 任务 7731000: 修改订单失败: 重新下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history，查询订单状态
2025/06/12 10:35:21 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756977637, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:35:21 hedge_task.go:976: 🚫 任务 7731000: 订单已被取消
2025/06/12 10:35:21 hedge_task.go:889: 🔍 任务 7731000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 10:35:21 hedge_task.go:712: 🏁 任务 7731000: 订单执行器收到终结信号，退出监控循环
2025/06/12 10:35:21 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:21 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:35:21 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:21 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2770.554, 触发源: price_trigger
2025/06/12 10:35:23 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.55, 订单ID: 8389765903756994912, 模式: POST_ONLY
2025/06/12 10:35:23 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2772.11
2025/06/12 10:35:23 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903756994912
2025/06/12 10:35:23 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:23 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:23 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:23 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:23 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:24 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:886: ❌ 任务 7731000: 修改订单失败: 重新下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history，查询订单状态
2025/06/12 10:35:25 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:25 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903756994912, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:35:25 hedge_task.go:976: 🚫 任务 7731000: 订单已被取消
2025/06/12 10:35:25 hedge_task.go:889: 🔍 任务 7731000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 10:35:25 hedge_task.go:712: 🏁 任务 7731000: 订单执行器收到终结信号，退出监控循环
2025/06/12 10:35:25 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:25 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:35:25 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:25 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2770.554, 触发源: price_trigger
2025/06/12 10:35:26 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.55, 订单ID: 8389765903757025956, 模式: POST_ONLY
2025/06/12 10:35:26 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2771.7
2025/06/12 10:35:27 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:27 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:27 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:27 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:28 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903757025956
2025/06/12 10:35:28 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:28 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:28 hedge_task.go:886: ❌ 任务 7731000: 修改订单失败: 重新下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history，查询订单状态
2025/06/12 10:35:28 hedge_task.go:497: ⚠️ 任务 7731000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:35:28 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903757025956, 状态: CANCELED, 原因: modify_failed
2025/06/12 10:35:28 hedge_task.go:976: 🚫 任务 7731000: 订单已被取消
2025/06/12 10:35:28 hedge_task.go:889: 🔍 任务 7731000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 10:35:28 hedge_task.go:712: 🏁 任务 7731000: 订单执行器收到终结信号，退出监控循环
2025/06/12 10:35:28 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:28 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:35:28 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:28 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2770.554, 触发源: price_trigger
2025/06/12 10:35:29 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2770.55, 订单ID: 8389765903757035813, 模式: POST_ONLY
2025/06/12 10:35:29 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2770.55, 新盘口: 2771.53
2025/06/12 10:35:29 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903757035813
2025/06/12 10:35:29 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2771.53, 订单ID: 8389765903757036601, 模式: POST_ONLY
2025/06/12 10:35:29 binance_client.go:153: ✅ 订单修改成功: ETHUSDT, 原订单ID: 8389765903757035813, 新订单ID: 8389765903757036601, 新价格: 2771.53
2025/06/12 10:35:30 hedge_task.go:851: 📊 任务 7731000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2771.53, 新盘口: 2771.47
2025/06/12 10:35:30 hedge_task.go:886: ❌ 任务 7731000: 修改订单失败: 撤销原订单失败: 撤销订单失败: <APIError> code=-2011, msg=Unknown order sent.，查询订单状态
2025/06/12 10:35:30 hedge_task.go:960: 📊 任务 7731000: 订单状态查询 - 订单ID: 8389765903757036601, 状态: FILLED, 原因: modify_failed
2025/06/12 10:35:30 hedge_task.go:1292: 📊 任务 7731000: 仓位已更新 - long 0.01
2025/06/12 10:35:31 hedge_task.go:1250: 📝 任务 7731000: 事件已记录 - 类型: open, 价格: 2771.53, 手续费: 0.00554306
2025/06/12 10:35:31 hedge_task.go:889: 🔍 任务 7731000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 10:35:31 hedge_task.go:712: 🏁 任务 7731000: 订单执行器收到终结信号，退出监控循环
2025/06/12 10:35:31 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:31 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:35:31 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 10:35:36 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:35:36 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:36 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2769.446, 触发源: price_trigger
2025/06/12 10:35:36 hedge_task.go:689: ❌ 任务 7731000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:35:36 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:36 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:35:36 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:36 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2769.446, 触发源: price_trigger
2025/06/12 10:35:37 hedge_task.go:689: ❌ 任务 7731000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:35:37 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:37 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:35:37 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:37 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2769.446, 触发源: price_trigger
2025/06/12 10:35:38 hedge_task.go:689: ❌ 任务 7731000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:35:38 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:35:38 hedge_task.go:612: 🎯 任务 7731000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:35:38 hedge_task.go:643: 🚀 任务 7731000: 订单执行器启动
2025/06/12 10:35:38 hedge_task.go:669: 📈 任务 7731000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2769.446, 触发源: price_trigger
2025/06/12 10:35:39 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 10:35:39 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 10:35:39 manager.go:141: 停止对冲任务: 7731000
2025/06/12 10:35:39 hedge_task.go:69: 📢 任务 7731000: 收到停止信号
2025/06/12 10:35:39 orderbook_manager.go:104: 📉 任务 7731000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 10:35:39 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:35:39 hedge_task.go:71: 🛑 对冲任务结束: 7731000
2025/06/12 10:35:39 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 10:35:39 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 10:35:39 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 10:35:39 hedge_task.go:689: ❌ 任务 7731000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:35:39 hedge_task.go:640: 🔓 任务 7731000: 订单执行器已退出，资源已清理
2025/06/12 10:52:45 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 10:52:45 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 10:52:45 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 10:52:45 main.go:195: API接口:
2025/06/12 10:52:45 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 10:52:45 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 10:52:45 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 10:52:45 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 10:52:45 main.go:200: 期权系统API接口:
2025/06/12 10:52:45 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 10:52:45 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 10:52:45 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 10:52:45 main.go:389: 
2025/06/12 10:52:45 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 10:52:45 api.go:35: API服务器启动，端口: 5879
2025/06/12 10:52:45 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 10:52:45 main.go:393:       示例: add long 3500 0.0005
2025/06/12 10:52:45 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 10:52:45 main.go:395: 
2025/06/12 10:52:45 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 10:52:45 main.go:397:    list                                     - 列出所有任务
2025/06/12 10:52:45 main.go:398: 
2025/06/12 10:52:45 main.go:399: ⚙️ 参数配置:
2025/06/12 10:52:45 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 10:52:45 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 10:52:45 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 10:52:45 main.go:403: 
2025/06/12 10:52:45 main.go:404: 📊 状态控制:
2025/06/12 10:52:45 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 10:52:45 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 10:52:45 main.go:407:    status off                               - 停止状态输出
2025/06/12 10:52:45 main.go:408: 
2025/06/12 10:52:45 main.go:409: 📋 数据导出:
2025/06/12 10:52:45 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 10:52:45 main.go:411: 
2025/06/12 10:52:45 main.go:412: 💾 数据管理:
2025/06/12 10:52:45 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 10:52:45 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 10:52:45 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 10:52:45 main.go:416: 
2025/06/12 10:52:45 main.go:417: 📋 事件查看:
2025/06/12 10:52:45 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 10:52:45 main.go:419: 
2025/06/12 10:52:45 main.go:420: 🎛️  批量控制:
2025/06/12 10:52:45 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 10:52:45 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 10:52:45 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 10:52:45 main.go:424: 
2025/06/12 10:52:45 main.go:425: 🗑️  任务管理:
2025/06/12 10:52:45 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 10:52:45 main.go:427: 
2025/06/12 10:52:45 main.go:428: 🔧 其他:
2025/06/12 10:52:45 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 10:52:45 main.go:430:    quit | exit                              - 退出程序
2025/06/12 10:52:45 main.go:431: ========================
2025/06/12 10:52:45 main.go:432: 
2025/06/12 10:52:47 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 10:52:47 main.go:728: 
2025/06/12 10:52:47 main.go:827: 7731000 停止   L    2770 0.020% 0.010  L0.01                 1  10:34  18m   0.00     0.98   0.01 是   manual   -                     
2025/06/12 10:52:47 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 10:53:05 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 10:53:05 状态变化:  -> running | 原因: 任务创建
2025/06/12 10:53:05 manager.go:94: 创建对冲任务: 7273000, 交易对: ETHUSDT, 方向: long, 目标价: 2766, 来源: manual
2025/06/12 10:53:05 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2766.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 10:53:05 hedge_task.go:45: 🚀 启动对冲任务: 7273000 (新版本)
2025/06/12 10:53:05 hedge_task.go:479: 📊 任务 7273000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 10:53:05 orderbook_manager.go:67: 📈 任务 7273000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 10:53:05 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:53:05 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 10:53:05 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 10:53:05 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 10:53:06 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 10:53:06 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:06 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2766.5532, 触发源: price_trigger
2025/06/12 10:53:07 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:08 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:09 binance_client.go:105: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2766.55, 订单ID: 8389765903762525355, 模式: POST_ONLY
2025/06/12 10:53:09 hedge_task.go:851: 📊 任务 7273000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2766.55, 新盘口: 2767.75
2025/06/12 10:53:09 hedge_task.go:884: 🎯 任务 7273000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 10:53:09 binance_client.go:197: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903762525355, 数量=0.01, priceMatch=QUEUE
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 binance_client.go:234: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903762525355, 数量: 0.01, priceMatch: QUEUE
2025/06/12 10:53:10 hedge_task.go:851: 📊 任务 7273000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2767.8, 新盘口: 2767.75
2025/06/12 10:53:10 hedge_task.go:884: 🎯 任务 7273000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 10:53:10 binance_client.go:197: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903762525355, 数量=0.01, priceMatch=QUEUE
2025/06/12 10:53:10 hedge_task.go:895: ❌ 任务 7273000: 修改订单失败: 修改订单失败: <APIError> code=-5027, msg=No need to modify the order.，查询订单状态
2025/06/12 10:53:10 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:10 hedge_task.go:973: 📊 任务 7273000: 订单状态查询 - 订单ID: 8389765903762525355, 状态: NEW, 原因: modify_failed
2025/06/12 10:53:10 hedge_task.go:897: 🔍 任务 7273000: 修改失败后订单状态查询结果，是否终结: false
2025/06/12 10:53:10 hedge_task.go:851: 📊 任务 7273000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2767.8, 新盘口: 2767.75
2025/06/12 10:53:10 hedge_task.go:884: 🎯 任务 7273000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 10:53:10 binance_client.go:197: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903762525355, 数量=0.01, priceMatch=QUEUE
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:11 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:895: ❌ 任务 7273000: 修改订单失败: 修改订单失败: <APIError> code=-2013, msg=Order does not exist.，查询订单状态
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:973: 📊 任务 7273000: 订单状态查询 - 订单ID: 8389765903762525355, 状态: FILLED, 原因: modify_failed
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:1305: 📊 任务 7273000: 仓位已更新 - long 0.01
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:12 hedge_task.go:1263: 📝 任务 7273000: 事件已记录 - 类型: open, 价格: 2767.8, 手续费: 0.0055356
2025/06/12 10:53:12 hedge_task.go:897: 🔍 任务 7273000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 10:53:12 hedge_task.go:712: 🏁 任务 7273000: 订单执行器收到终结信号，退出监控循环
2025/06/12 10:53:12 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:12 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 10:53:12 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/12 10:53:16 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:16 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:16 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:16 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:16 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:16 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:16 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:16 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:17 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:17 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:17 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:17 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:17 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:18 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:18 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:18 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:18 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:18 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:19 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:19 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:19 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:19 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:19 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:20 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:20 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:20 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:21 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:22 hedge_task.go:497: ⚠️ 任务 7273000: 订单执行器通道满，跳过盘口数据
2025/06/12 10:53:22 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:22 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 10:53:22 hedge_task.go:612: 🎯 任务 7273000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 10:53:22 hedge_task.go:643: 🚀 任务 7273000: 订单执行器启动
2025/06/12 10:53:22 hedge_task.go:669: 📈 任务 7273000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2765.4468, 触发源: price_trigger
2025/06/12 10:53:22 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 10:53:22 main.go:225: 停止任务 7731000 失败: 任务已停止: 7731000
2025/06/12 10:53:22 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 10:53:22 manager.go:141: 停止对冲任务: 7273000
2025/06/12 10:53:22 hedge_task.go:69: 📢 任务 7273000: 收到停止信号
2025/06/12 10:53:22 orderbook_manager.go:104: 📉 任务 7273000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 10:53:22 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 10:53:22 hedge_task.go:71: 🛑 对冲任务结束: 7273000
2025/06/12 10:53:22 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 10:53:22 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 10:53:22 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/12 10:53:23 hedge_task.go:689: ❌ 任务 7273000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 10:53:23 hedge_task.go:640: 🔓 任务 7273000: 订单执行器已退出，资源已清理
2025/06/12 11:14:04 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 11:14:04 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 11:14:04 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 11:14:04 main.go:195: API接口:
2025/06/12 11:14:04 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 11:14:04 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 11:14:04 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 11:14:04 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 11:14:04 main.go:200: 期权系统API接口:
2025/06/12 11:14:04 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 11:14:04 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 11:14:04 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 11:14:04 main.go:389: 
2025/06/12 11:14:04 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 11:14:04 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 11:14:04 main.go:393:       示例: add long 3500 0.0005
2025/06/12 11:14:04 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 11:14:04 main.go:395: 
2025/06/12 11:14:04 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 11:14:04 main.go:397:    list                                     - 列出所有任务
2025/06/12 11:14:04 main.go:398: 
2025/06/12 11:14:04 main.go:399: ⚙️ 参数配置:
2025/06/12 11:14:04 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 11:14:04 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 11:14:04 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 11:14:04 main.go:403: 
2025/06/12 11:14:04 main.go:404: 📊 状态控制:
2025/06/12 11:14:04 api.go:35: API服务器启动，端口: 5879
2025/06/12 11:14:04 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 11:14:04 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 11:14:04 main.go:407:    status off                               - 停止状态输出
2025/06/12 11:14:04 main.go:408: 
2025/06/12 11:14:04 main.go:409: 📋 数据导出:
2025/06/12 11:14:04 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 11:14:04 main.go:411: 
2025/06/12 11:14:04 main.go:412: 💾 数据管理:
2025/06/12 11:14:04 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 11:14:04 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 11:14:04 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 11:14:04 main.go:416: 
2025/06/12 11:14:04 main.go:417: 📋 事件查看:
2025/06/12 11:14:04 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 11:14:04 main.go:419: 
2025/06/12 11:14:04 main.go:420: 🎛️  批量控制:
2025/06/12 11:14:04 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 11:14:04 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 11:14:04 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 11:14:04 main.go:424: 
2025/06/12 11:14:04 main.go:425: 🗑️  任务管理:
2025/06/12 11:14:04 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 11:14:04 main.go:427: 
2025/06/12 11:14:04 main.go:428: 🔧 其他:
2025/06/12 11:14:04 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 11:14:04 main.go:430:    quit | exit                              - 退出程序
2025/06/12 11:14:04 main.go:431: ========================
2025/06/12 11:14:04 main.go:432: 
2025/06/12 11:14:08 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:14:08 main.go:728: 
2025/06/12 11:14:08 main.go:827: 7273000 停止   L    2766 0.020% 0.010  L0.01                 1  10:53  21m   0.00     1.25   0.01 是   manual   -                     
2025/06/12 11:14:08 main.go:827: 7731000 停止   L    2770 0.020% 0.010  L0.01                 1  10:34  39m   0.00     0.98   0.01 是   manual   -                     
2025/06/12 11:14:08 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:14:21 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 11:14:21 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:14:21 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:14:26 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 11:14:26 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/12 11:14:26 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:14:35 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 11:14:35 状态变化:  -> running | 原因: 任务创建
2025/06/12 11:14:35 manager.go:94: 创建对冲任务: 8320000, 交易对: ETHUSDT, 方向: long, 目标价: 2750, 来源: manual
2025/06/12 11:14:35 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2750.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 11:14:35 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 11:14:35 hedge_task.go:45: 🚀 启动对冲任务: 8320000 (新版本)
2025/06/12 11:14:35 hedge_task.go:479: 📊 任务 8320000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 11:14:35 orderbook_manager.go:67: 📈 任务 8320000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 11:14:35 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:14:35 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:14:35 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:14:36 hedge_task.go:612: 🎯 任务 8320000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 11:14:36 hedge_task.go:643: 🚀 任务 8320000: 订单执行器启动
2025/06/12 11:14:36 hedge_task.go:669: 📈 任务 8320000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2750.55, 触发源: price_trigger
2025/06/12 11:14:37 binance_client.go:105: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2750.55, 订单ID: 8389765903771258063, 模式: POST_ONLY
2025/06/12 11:14:37 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2750.55, 新盘口: 2759.01
2025/06/12 11:14:37 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:37 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:37 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903771258063, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:14:37 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.23, 新盘口: 2759.01
2025/06/12 11:14:37 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:37 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:37 binance_client.go:227: ℹ️ 订单 8389765903771258063 无需修改，价格已经是最优价格
2025/06/12 11:14:37 hedge_task.go:908: ℹ️ 任务 8320000: 订单无需修改，价格已经是最优价格: 2759.23 (修改次数: 1)
2025/06/12 11:14:37 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.23, 新盘口: 2759.01
2025/06/12 11:14:37 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:37 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:38 binance_client.go:227: ℹ️ 订单 8389765903771258063 无需修改，价格已经是最优价格
2025/06/12 11:14:38 hedge_task.go:908: ℹ️ 任务 8320000: 订单无需修改，价格已经是最优价格: 2759.23 (修改次数: 1)
2025/06/12 11:14:38 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.23, 新盘口: 2759.2
2025/06/12 11:14:38 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:38 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:38 binance_client.go:227: ℹ️ 订单 8389765903771258063 无需修改，价格已经是最优价格
2025/06/12 11:14:38 hedge_task.go:908: ℹ️ 任务 8320000: 订单无需修改，价格已经是最优价格: 2759.23 (修改次数: 1)
2025/06/12 11:14:41 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.23, 新盘口: 2759.25
2025/06/12 11:14:41 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:41 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:41 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903771258063, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:14:42 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.25, 新盘口: 2759.28
2025/06/12 11:14:42 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:42 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:42 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903771258063, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:14:42 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:42 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:43 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:43 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:44 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:44 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:45 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:45 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:45 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.28, 新盘口: 2759.44
2025/06/12 11:14:45 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:45 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:46 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903771258063, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:14:46 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:46 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:47 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:47 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:48 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.44, 新盘口: 2759.51
2025/06/12 11:14:48 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:48 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:48 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903771258063, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:14:48 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:48 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:48 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.56, 新盘口: 2759.53
2025/06/12 11:14:48 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:48 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:49 binance_client.go:227: ℹ️ 订单 8389765903771258063 无需修改，价格已经是最优价格
2025/06/12 11:14:49 hedge_task.go:908: ℹ️ 任务 8320000: 订单无需修改，价格已经是最优价格: 2759.56 (修改次数: 5)
2025/06/12 11:14:49 hedge_task.go:719: ⏰ 任务 8320000: 订单超时，查询订单状态
2025/06/12 11:14:49 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: NEW, 原因: timeout
2025/06/12 11:14:49 hedge_task.go:851: 📊 任务 8320000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2759.56, 新盘口: 2759.24
2025/06/12 11:14:49 hedge_task.go:884: 🎯 任务 8320000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:14:49 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903771258063, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:14:50 hedge_task.go:895: ❌ 任务 8320000: 修改订单失败: 修改订单失败: <APIError> code=-2013, msg=Order does not exist.，查询订单状态
2025/06/12 11:14:50 hedge_task.go:981: 📊 任务 8320000: 订单状态查询 - 订单ID: 8389765903771258063, 状态: FILLED, 原因: modify_failed
2025/06/12 11:14:50 hedge_task.go:1313: 📊 任务 8320000: 仓位已更新 - long 0.01
2025/06/12 11:14:50 hedge_task.go:1271: 📝 任务 8320000: 事件已记录 - 类型: open, 价格: 2759.56, 手续费: 0.00551912
2025/06/12 11:14:50 hedge_task.go:897: 🔍 任务 8320000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 11:14:50 hedge_task.go:712: 🏁 任务 8320000: 订单执行器收到终结信号，退出监控循环
2025/06/12 11:14:50 hedge_task.go:640: 🔓 任务 8320000: 订单执行器已退出，资源已清理
2025/06/12 11:14:50 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:14:50 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:18:10 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:18:10 main.go:728: 
2025/06/12 11:18:10 main.go:827: 8320000 运行   L    2750 0.020% 0.010  L0.01                 1  11:14   4m   0.00     9.01   0.01 是   manual   -                     
2025/06/12 11:18:10 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:18:56 hedge_task.go:69: 📢 任务 8320000: 收到停止信号
2025/06/12 11:18:56 hedge_task.go:418: 🛑 任务 8320000: 开始停止平仓，当前仓位: long 0.01
2025/06/12 11:18:56 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 11:18:56 hedge_task.go:612: 🎯 任务 8320000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 11:18:56 orderbook_manager.go:104: 📉 任务 8320000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 11:18:56 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:18:56 hedge_task.go:643: 🚀 任务 8320000: 订单执行器启动
2025/06/12 11:18:56 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 11:18:56 hedge_task.go:71: 🛑 对冲任务结束: 8320000
2025/06/12 11:18:56 hedge_task.go:669: 📈 任务 8320000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2756.94, 触发源: stop_close
2025/06/12 11:18:56 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/12 11:18:56 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:18:57 binance_client.go:105: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2756.94, 订单ID: 8389765903772913230, 模式: POST_ONLY
2025/06/12 11:18:57 hedge_task.go:727: 🛑 任务 8320000: 收到停止信号，取消订单
2025/06/12 11:18:57 binance_client.go:141: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903772913230
2025/06/12 11:18:57 hedge_task.go:640: 🔓 任务 8320000: 订单执行器已退出，资源已清理
2025/06/12 11:19:04 data_manager.go:184: 💾 任务数据已保存 (0个任务)
2025/06/12 11:19:04 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:19:23 main.go:533: ❌ 参数不足，格式: add <long|short> <目标价> <阈值> <数量> [可选参数...]
2025/06/12 11:19:23 main.go:534:    示例: add long 3500 0.002 0.01
2025/06/12 11:19:23 main.go:535:    可选参数: slippage=0.01 stop_slippage=0.01 timeout=5 volume=0.5
2025/06/12 11:19:26 main.go:694: 📋 当前没有运行的任务
2025/06/12 11:19:33 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 11:19:33 状态变化:  -> running | 原因: 任务创建
2025/06/12 11:19:33 manager.go:94: 创建对冲任务: 7313000, 交易对: ETHUSDT, 方向: long, 目标价: 2756, 来源: manual
2025/06/12 11:19:33 hedge_task.go:45: 🚀 启动对冲任务: 7313000 (新版本)
2025/06/12 11:19:33 hedge_task.go:479: 📊 任务 7313000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 11:19:33 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2756.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 11:19:33 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 11:19:33 orderbook_manager.go:67: 📈 任务 7313000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 11:19:33 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:19:33 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:19:33 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:19:34 hedge_task.go:612: 🎯 任务 7313000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 11:19:34 hedge_task.go:643: 🚀 任务 7313000: 订单执行器启动
2025/06/12 11:19:34 hedge_task.go:669: 📈 任务 7313000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2756.5512, 触发源: price_trigger
2025/06/12 11:19:35 binance_client.go:105: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2756.55, 订单ID: 8389765903773175571, 模式: POST_ONLY
2025/06/12 11:19:35 hedge_task.go:851: 📊 任务 7313000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.55, 新盘口: 2757.4
2025/06/12 11:19:35 hedge_task.go:884: 🎯 任务 7313000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:19:35 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903773175571, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:19:35 binance_client.go:256: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903773175571, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:19:35 hedge_task.go:851: 📊 任务 7313000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2757.19, 新盘口: 2757.4
2025/06/12 11:19:35 hedge_task.go:884: 🎯 任务 7313000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:19:35 binance_client.go:208: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903773175571, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:19:36 hedge_task.go:895: ❌ 任务 7313000: 修改订单失败: 修改订单失败: <APIError> code=-2013, msg=Order does not exist.，查询订单状态
2025/06/12 11:19:36 hedge_task.go:981: 📊 任务 7313000: 订单状态查询 - 订单ID: 8389765903773175571, 状态: FILLED, 原因: modify_failed
2025/06/12 11:19:37 hedge_task.go:1313: 📊 任务 7313000: 仓位已更新 - long 0.01
2025/06/12 11:19:37 hedge_task.go:1271: 📝 任务 7313000: 事件已记录 - 类型: open, 价格: 2757.19, 手续费: 0.00551438
2025/06/12 11:19:37 hedge_task.go:897: 🔍 任务 7313000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 11:19:37 hedge_task.go:712: 🏁 任务 7313000: 订单执行器收到终结信号，退出监控循环
2025/06/12 11:19:37 hedge_task.go:640: 🔓 任务 7313000: 订单执行器已退出，资源已清理
2025/06/12 11:19:37 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:19:37 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:19:45 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:19:45 main.go:728: 
2025/06/12 11:19:45 main.go:827: 7313000 运行   L    2756 0.020% 0.010  L0.01                 1  11:19   0m   0.00     0.64   0.01 是   manual   -                     
2025/06/12 11:19:45 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:19:47 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:19:47 main.go:728: 
2025/06/12 11:19:47 main.go:827: 7313000 运行   L    2756 0.020% 0.010  L0.01                 1  11:19   0m   0.00     0.64   0.01 是   manual   -                     
2025/06/12 11:19:47 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:20:07 hedge_task.go:612: 🎯 任务 7313000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 11:20:07 hedge_task.go:643: 🚀 任务 7313000: 订单执行器启动
2025/06/12 11:20:07 hedge_task.go:669: 📈 任务 7313000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2755.4488, 触发源: price_trigger
2025/06/12 11:20:07 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:20:07 main.go:728: 
2025/06/12 11:20:07 main.go:827: 7313000 运行   L    2756 0.020% 0.010  L0.01                 1  11:19   1m   0.00     0.64   0.01 是   manual   -                     
2025/06/12 11:20:07 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:20:08 hedge_task.go:689: ❌ 任务 7313000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:20:08 hedge_task.go:640: 🔓 任务 7313000: 订单执行器已退出，资源已清理
2025/06/12 11:20:08 hedge_task.go:612: 🎯 任务 7313000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 11:20:08 hedge_task.go:643: 🚀 任务 7313000: 订单执行器启动
2025/06/12 11:20:08 hedge_task.go:669: 📈 任务 7313000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2755.4488, 触发源: price_trigger
2025/06/12 11:20:09 hedge_task.go:689: ❌ 任务 7313000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:20:09 hedge_task.go:640: 🔓 任务 7313000: 订单执行器已退出，资源已清理
2025/06/12 11:20:09 hedge_task.go:612: 🎯 任务 7313000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 11:20:09 hedge_task.go:643: 🚀 任务 7313000: 订单执行器启动
2025/06/12 11:20:09 hedge_task.go:669: 📈 任务 7313000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2755.4488, 触发源: price_trigger
2025/06/12 11:20:10 hedge_task.go:689: ❌ 任务 7313000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:20:10 hedge_task.go:640: 🔓 任务 7313000: 订单执行器已退出，资源已清理
2025/06/12 11:20:10 hedge_task.go:612: 🎯 任务 7313000: 启动订单执行器 - 现有: long 0.01, 应有: none 0
2025/06/12 11:20:10 hedge_task.go:643: 🚀 任务 7313000: 订单执行器启动
2025/06/12 11:20:10 hedge_task.go:669: 📈 任务 7313000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2755.4488, 触发源: price_trigger
2025/06/12 11:20:12 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 11:20:12 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 11:20:12 manager.go:141: 停止对冲任务: 7313000
2025/06/12 11:20:12 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:20:12 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:20:12 hedge_task.go:69: 📢 任务 7313000: 收到停止信号
2025/06/12 11:20:12 orderbook_manager.go:104: 📉 任务 7313000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 11:20:12 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:20:12 hedge_task.go:71: 🛑 对冲任务结束: 7313000
2025/06/12 11:20:12 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 11:33:32 main.go:177: 启动API服务模式（包含交互功能），端口: 5879
2025/06/12 11:33:32 main.go:178: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/12 11:33:32 main.go:194: API服务器已启动，访问地址: http://localhost:5879
2025/06/12 11:33:32 main.go:195: API接口:
2025/06/12 11:33:32 main.go:196:   POST /task                           - 创建对冲任务
2025/06/12 11:33:32 main.go:197:   POST /task/stop                      - 停止对冲任务
2025/06/12 11:33:32 main.go:198:   GET  /tasks                          - 获取所有任务
2025/06/12 11:33:32 main.go:199:   GET  /task/{id}                      - 获取单个任务
2025/06/12 11:33:32 main.go:200: 期权系统API接口:
2025/06/12 11:33:32 main.go:201:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/12 11:33:32 main.go:202:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/12 11:33:32 main.go:203:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/12 11:33:32 main.go:389: 
2025/06/12 11:33:32 main.go:390: 🔧 === 交互命令帮助 ===
2025/06/12 11:33:32 main.go:392:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/12 11:33:32 main.go:393:       示例: add long 3500 0.0005
2025/06/12 11:33:32 main.go:394:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/12 11:33:32 main.go:395: 
2025/06/12 11:33:32 main.go:396:    stop <task_id>                           - 停止指定任务
2025/06/12 11:33:32 main.go:397:    list                                     - 列出所有任务
2025/06/12 11:33:32 main.go:398: 
2025/06/12 11:33:32 main.go:399: ⚙️ 参数配置:
2025/06/12 11:33:32 main.go:400:    set <参数名> <值>                         - 设置默认参数
2025/06/12 11:33:32 main.go:401:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/12 11:33:32 main.go:402:    show defaults                            - 显示当前默认参数
2025/06/12 11:33:32 main.go:403: 
2025/06/12 11:33:32 main.go:404: 📊 状态控制:
2025/06/12 11:33:32 main.go:405:    status                                   - 查看状态输出设置
2025/06/12 11:33:32 main.go:406:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/12 11:33:32 main.go:407:    status off                               - 停止状态输出
2025/06/12 11:33:32 main.go:408: 
2025/06/12 11:33:32 api.go:35: API服务器启动，端口: 5879
2025/06/12 11:33:32 main.go:409: 📋 数据导出:
2025/06/12 11:33:32 main.go:410:    export | detail                          - 导出详细状态到剪切板
2025/06/12 11:33:32 main.go:411: 
2025/06/12 11:33:32 main.go:412: 💾 数据管理:
2025/06/12 11:33:32 main.go:413:    save                                     - 手动保存数据到本地文件
2025/06/12 11:33:32 main.go:414:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/12 11:33:32 main.go:415:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/12 11:33:32 main.go:416: 
2025/06/12 11:33:32 main.go:417: 📋 事件查看:
2025/06/12 11:33:32 main.go:418:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/12 11:33:32 main.go:419: 
2025/06/12 11:33:32 main.go:420: 🎛️  批量控制:
2025/06/12 11:33:32 main.go:421:    startall                                 - 启动所有已停止的任务
2025/06/12 11:33:32 main.go:422:    stopall                                  - 停止所有运行中的任务
2025/06/12 11:33:32 main.go:423:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/12 11:33:32 main.go:424: 
2025/06/12 11:33:32 main.go:425: 🗑️  任务管理:
2025/06/12 11:33:32 main.go:426:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/12 11:33:32 main.go:427: 
2025/06/12 11:33:32 main.go:428: 🔧 其他:
2025/06/12 11:33:32 main.go:429:    help                                     - 显示此帮助信息
2025/06/12 11:33:32 main.go:430:    quit | exit                              - 退出程序
2025/06/12 11:33:32 main.go:431: ========================
2025/06/12 11:33:32 main.go:432: 
2025/06/12 11:34:15 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 11:34:15 状态变化:  -> running | 原因: 任务创建
2025/06/12 11:34:15 manager.go:94: 创建对冲任务: 8336000, 交易对: ETHUSDT, 方向: long, 目标价: 2755, 来源: manual
2025/06/12 11:34:15 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2755.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 11:34:15 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 11:34:15 hedge_task.go:45: 🚀 启动对冲任务: 8336000 (新版本)
2025/06/12 11:34:15 hedge_task.go:479: 📊 任务 8336000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 11:34:15 orderbook_manager.go:67: 📈 任务 8336000 订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 11:34:15 orderbook_manager.go:117: 🔌 启动交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:34:15 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 11:34:15 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:34:20 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:34:20 main.go:728: 
2025/06/12 11:34:20 main.go:827: 7313000 停止   L    2756 0.020% 0.010  L0.01                 1  11:19  15m   0.00     0.64   0.01 是   manual   -                     
2025/06/12 11:34:20 main.go:827: 8336000 运行   L    2755 0.020% 0.010  无仓位                   0  11:34   0m   0.00     0.00   0.00 是   manual   -                     
2025/06/12 11:34:20 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:34:30 状态变化: stopped -> DELETED | 原因: 任务已删除
2025/06/12 11:34:30 data_manager.go:184: 💾 任务数据已保存 (1个任务)
2025/06/12 11:34:30 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:35:02 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 11:35:02 状态变化:  -> running | 原因: 任务创建
2025/06/12 11:35:02 manager.go:94: 创建对冲任务: 2564000, 交易对: ETHUSDT, 方向: long, 目标价: 2753, 来源: manual
2025/06/12 11:35:02 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=long, 目标价=2753.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 11:35:02 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 11:35:02 hedge_task.go:45: 🚀 启动对冲任务: 2564000 (新版本)
2025/06/12 11:35:02 hedge_task.go:479: 📊 任务 2564000: 三仓位状态已初始化 - 设定: long 0.01
2025/06/12 11:35:02 orderbook_manager.go:67: 📈 任务 2564000 订阅交易对 ETHUSDT (引用计数: 2)
2025/06/12 11:35:02 hedge_task.go:612: 🎯 任务 2564000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 11:35:02 hedge_task.go:643: 🚀 任务 2564000: 订单执行器启动
2025/06/12 11:35:02 hedge_task.go:669: 📈 任务 2564000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2753.5506, 触发源: price_trigger
2025/06/12 11:35:02 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 11:35:02 data_manager.go:228: 💾 事件数据已保存 (0个事件)
2025/06/12 11:35:03 hedge_task.go:612: 🎯 任务 8336000: 启动订单执行器 - 现有: none 0, 应有: long 0.01
2025/06/12 11:35:03 hedge_task.go:643: 🚀 任务 8336000: 订单执行器启动
2025/06/12 11:35:03 hedge_task.go:669: 📈 任务 8336000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2755.551, 触发源: price_trigger
2025/06/12 11:35:03 binance_client.go:105: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2753.55, 订单ID: 8389765903779373117, 模式: POST_ONLY
2025/06/12 11:35:03 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2753.55, 新盘口: 2754.19
2025/06/12 11:35:03 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:03 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:03 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779373117, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:03 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2754.33
2025/06/12 11:35:03 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:03 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:04 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:04 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:04 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2754.55
2025/06/12 11:35:04 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:04 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:04 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:04 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:04 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2754.67
2025/06/12 11:35:04 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:04 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:04 binance_client.go:105: ✅ 限价单已下达: ETHUSDT BUY 0.01 @ 2755.55, 订单ID: 8389765903779374267, 模式: POST_ONLY
2025/06/12 11:35:04 hedge_task.go:851: 📊 任务 8336000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.55, 新盘口: 2755.93
2025/06/12 11:35:04 hedge_task.go:884: 🎯 任务 8336000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:04 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779374267, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:04 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:04 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:04 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2755.34
2025/06/12 11:35:04 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:04 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:05 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:05 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:05 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2755.35
2025/06/12 11:35:05 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:05 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:05 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779374267, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:05 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:05 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:05 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2755.35
2025/06/12 11:35:05 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:05 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:06 binance_client.go:285: ℹ️ 订单 8389765903779373117 无需修改，价格已经是最优价格
2025/06/12 11:35:06 hedge_task.go:908: ℹ️ 任务 2564000: 订单无需修改，价格已经是最优价格: 2755.93 (修改次数: 1)
2025/06/12 11:35:06 hedge_task.go:851: 📊 任务 2564000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2755.35
2025/06/12 11:35:06 hedge_task.go:884: 🎯 任务 2564000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:06 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779373117, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:06 hedge_task.go:895: ❌ 任务 2564000: 修改订单失败: 修改订单失败: <APIError> code=-2013, msg=Order does not exist.，查询订单状态
2025/06/12 11:35:08 hedge_task.go:851: 📊 任务 8336000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.93, 新盘口: 2755.99
2025/06/12 11:35:08 hedge_task.go:884: 🎯 任务 8336000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:08 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779374267, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:08 hedge_task.go:981: 📊 任务 2564000: 订单状态查询 - 订单ID: 8389765903779373117, 状态: FILLED, 原因: modify_failed
2025/06/12 11:35:08 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779374267, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:08 hedge_task.go:1313: 📊 任务 2564000: 仓位已更新 - long 0.01
2025/06/12 11:35:09 hedge_task.go:1271: 📝 任务 2564000: 事件已记录 - 类型: open, 价格: 2755.93, 手续费: 0.00551186
2025/06/12 11:35:09 hedge_task.go:897: 🔍 任务 2564000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 11:35:09 hedge_task.go:712: 🏁 任务 2564000: 订单执行器收到终结信号，退出监控循环
2025/06/12 11:35:09 hedge_task.go:640: 🔓 任务 2564000: 订单执行器已退出，资源已清理
2025/06/12 11:35:09 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 11:35:09 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/12 11:35:09 hedge_task.go:719: ⏰ 任务 8336000: 订单超时，查询订单状态
2025/06/12 11:35:09 hedge_task.go:981: 📊 任务 8336000: 订单状态查询 - 订单ID: 8389765903779374267, 状态: NEW, 原因: timeout
2025/06/12 11:35:10 hedge_task.go:719: ⏰ 任务 8336000: 订单超时，查询订单状态
2025/06/12 11:35:10 hedge_task.go:981: 📊 任务 8336000: 订单状态查询 - 订单ID: 8389765903779374267, 状态: NEW, 原因: timeout
2025/06/12 11:35:10 hedge_task.go:851: 📊 任务 8336000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2755.99, 新盘口: 2756
2025/06/12 11:35:10 hedge_task.go:884: 🎯 任务 8336000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:10 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779374267, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:10 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779374267, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:11 hedge_task.go:719: ⏰ 任务 8336000: 订单超时，查询订单状态
2025/06/12 11:35:11 hedge_task.go:981: 📊 任务 8336000: 订单状态查询 - 订单ID: 8389765903779374267, 状态: NEW, 原因: timeout
2025/06/12 11:35:11 hedge_task.go:851: 📊 任务 8336000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.29, 新盘口: 2756.39
2025/06/12 11:35:11 hedge_task.go:884: 🎯 任务 8336000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:11 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779374267, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:12 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779374267, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:12 hedge_task.go:851: 📊 任务 8336000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.59, 新盘口: 2756.66
2025/06/12 11:35:12 hedge_task.go:884: 🎯 任务 8336000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:35:12 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779374267, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:35:12 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779374267, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:35:12 hedge_task.go:719: ⏰ 任务 8336000: 订单超时，查询订单状态
2025/06/12 11:35:12 hedge_task.go:981: 📊 任务 8336000: 订单状态查询 - 订单ID: 8389765903779374267, 状态: FILLED, 原因: timeout
2025/06/12 11:35:13 hedge_task.go:1313: 📊 任务 8336000: 仓位已更新 - long 0.01
2025/06/12 11:35:14 hedge_task.go:1271: 📝 任务 8336000: 事件已记录 - 类型: open, 价格: 2756.66, 手续费: 0.00551332
2025/06/12 11:35:14 hedge_task.go:640: 🔓 任务 8336000: 订单执行器已退出，资源已清理
2025/06/12 11:35:14 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/12 11:35:14 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/12 11:35:16 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:35:16 main.go:728: 
2025/06/12 11:35:16 main.go:827: 8336000 运行   L    2755 0.020% 0.010  L0.01                 1  11:34   1m   0.00     1.11   0.01 是   manual   -                     
2025/06/12 11:35:16 main.go:827: 2564000 运行   L    2753 0.020% 0.010  L0.01                 1  11:35   0m   0.00     2.38   0.01 是   manual   -                     
2025/06/12 11:35:16 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:35:40 main.go:1313: 🔍 [DEBUG] 任务 2564000 报告生成: 总盈亏原始值=0, 转换后=0.00
2025/06/12 11:35:40 main.go:1313: 🔍 [DEBUG] 任务 8336000 报告生成: 总盈亏原始值=0, 转换后=0.00
2025/06/12 11:35:40 main.go:1568: ✅ 报告已保存到文件: export/hedge_report_20250612_113540.txt
2025/06/12 11:35:40 main.go:1029: ✅ 详细状态已复制到剪切板
2025/06/12 11:35:40 main.go:1031: 📋 详细状态报告:
2025/06/12 11:36:22 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0100, 停止滑点=0.0100, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/12 11:36:22 状态变化:  -> running | 原因: 任务创建
2025/06/12 11:36:22 manager.go:94: 创建对冲任务: 5673000, 交易对: ETHUSDT, 方向: short, 目标价: 2758, 来源: manual
2025/06/12 11:36:22 hedge_task.go:45: 🚀 启动对冲任务: 5673000 (新版本)
2025/06/12 11:36:22 hedge_task.go:479: 📊 任务 5673000: 三仓位状态已初始化 - 设定: short 0.01
2025/06/12 11:36:22 orderbook_manager.go:67: 📈 任务 5673000 订阅交易对 ETHUSDT (引用计数: 3)
2025/06/12 11:36:22 main.go:662: 📊 任务参数: 交易对=ETHUSDT, 方向=short, 目标价=2758.00, 阈值=0.0200%, 数量=0.0100
2025/06/12 11:36:22 main.go:669: 📊 高级参数: 滑点=1.000%, 停止滑点=1.000%, 超时=5秒, 盘口阈值=50.0%, 限价模式=仅限挂单成交
2025/06/12 11:36:22 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/12 11:36:22 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/12 11:36:29 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: none 0, 应有: short 0.01
2025/06/12 11:36:29 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:36:29 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: short, 数量: 0.01, 理论价格: 2757.4484, 触发源: price_trigger
2025/06/12 11:36:31 binance_client.go:105: ✅ 限价单已下达: ETHUSDT SELL 0.01 @ 2757.44, 订单ID: 8389765903779776681, 模式: POST_ONLY
2025/06/12 11:36:31 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2757.44, 新盘口: 2756.92
2025/06/12 11:36:31 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:31 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:31 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779776681, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:36:31 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.79, 新盘口: 2756.92
2025/06/12 11:36:31 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:31 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:31 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:31 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.79 (修改次数: 1)
2025/06/12 11:36:31 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.79, 新盘口: 2756.8
2025/06/12 11:36:31 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:31 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:31 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:31 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.79 (修改次数: 1)
2025/06/12 11:36:31 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.79, 新盘口: 2756.8
2025/06/12 11:36:31 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:31 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:31 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779776681, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:36:31 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.47, 新盘口: 2756.8
2025/06/12 11:36:31 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:31 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:32 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:32 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.47 (修改次数: 2)
2025/06/12 11:36:32 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.47, 新盘口: 2756.8
2025/06/12 11:36:32 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:32 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:32 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:32 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.47 (修改次数: 2)
2025/06/12 11:36:32 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.47, 新盘口: 2756.8
2025/06/12 11:36:32 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:32 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:32 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:32 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.47 (修改次数: 2)
2025/06/12 11:36:32 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.47, 新盘口: 2756.79
2025/06/12 11:36:32 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:32 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:32 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:32 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.47 (修改次数: 2)
2025/06/12 11:36:32 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.47, 新盘口: 2756.79
2025/06/12 11:36:32 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:32 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:32 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779776681, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:36:32 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.39, 新盘口: 2756.79
2025/06/12 11:36:32 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:32 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:33 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:33 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.39 (修改次数: 3)
2025/06/12 11:36:33 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.39, 新盘口: 2756.79
2025/06/12 11:36:33 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:33 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:33 binance_client.go:285: ℹ️ 订单 8389765903779776681 无需修改，价格已经是最优价格
2025/06/12 11:36:33 hedge_task.go:908: ℹ️ 任务 5673000: 订单无需修改，价格已经是最优价格: 2756.39 (修改次数: 3)
2025/06/12 11:36:33 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.39, 新盘口: 2756.79
2025/06/12 11:36:33 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:33 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:34 binance_client.go:314: ✅ 订单修改成功(priceMatch): ETHUSDT, 订单ID: 8389765903779776681, 数量: 0.01, priceMatch: QUEUE
2025/06/12 11:36:34 hedge_task.go:851: 📊 任务 5673000: 盘口价格变化，尝试调整订单价格 - 当前挂单: 2756.16, 新盘口: 2756.79
2025/06/12 11:36:34 hedge_task.go:884: 🎯 任务 5673000: 使用QUEUE模式修改订单，让交易所自动设置排队价格
2025/06/12 11:36:34 binance_client.go:266: 🔄 开始修改订单(使用priceMatch): 订单ID=8389765903779776681, 数量=0.01, priceMatch=QUEUE
2025/06/12 11:36:36 hedge_task.go:895: ❌ 任务 5673000: 修改订单失败: 修改订单失败: <APIError> code=-2013, msg=Order does not exist.，查询订单状态
2025/06/12 11:36:37 hedge_task.go:981: 📊 任务 5673000: 订单状态查询 - 订单ID: 8389765903779776681, 状态: FILLED, 原因: modify_failed
2025/06/12 11:36:37 hedge_task.go:1313: 📊 任务 5673000: 仓位已更新 - short 0.01
2025/06/12 11:36:37 hedge_task.go:1271: 📝 任务 5673000: 事件已记录 - 类型: open, 价格: 2756.16, 手续费: 0.00551232
2025/06/12 11:36:37 hedge_task.go:897: 🔍 任务 5673000: 修改失败后订单状态查询结果，是否终结: true
2025/06/12 11:36:37 hedge_task.go:712: 🏁 任务 5673000: 订单执行器收到终结信号，退出监控循环
2025/06/12 11:36:37 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:36:37 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/12 11:36:37 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/12 11:36:49 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:36:49 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:36:49 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:36:52 hedge_task.go:689: ❌ 任务 5673000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:36:52 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:37:01 main.go:727: __________________________________________________________________________________________________________________________
2025/06/12 11:37:01 main.go:728: 
2025/06/12 11:37:01 main.go:827: 2564000 运行   L    2753 0.020% 0.010  L0.01                 1  11:35   2m   0.00     2.38   0.01 是   manual   -                     
2025/06/12 11:37:01 main.go:827: 8336000 运行   L    2755 0.020% 0.010  L0.01                 1  11:34   3m   0.00     1.11   0.01 是   manual   -                     
2025/06/12 11:37:01 main.go:827: 5673000 运行   S    2758 0.020% 0.010  S0.01                 1  11:36   1m   0.00     1.29   0.01 是   manual   -                     
2025/06/12 11:37:01 main.go:845: __________________________________________________________________________________________________________________________
2025/06/12 11:37:07 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:37:07 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:37:07 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:37:09 hedge_task.go:689: ❌ 任务 5673000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:37:09 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:37:09 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:37:09 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:37:09 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:37:10 hedge_task.go:689: ❌ 任务 5673000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:37:10 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:37:10 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:37:10 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:37:10 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:37:11 hedge_task.go:689: ❌ 任务 5673000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:37:11 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:37:11 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:37:11 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:37:11 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:37:16 hedge_task.go:689: ❌ 任务 5673000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/12 11:37:16 hedge_task.go:640: 🔓 任务 5673000: 订单执行器已退出，资源已清理
2025/06/12 11:37:17 hedge_task.go:612: 🎯 任务 5673000: 启动订单执行器 - 现有: short 0.01, 应有: none 0
2025/06/12 11:37:17 hedge_task.go:643: 🚀 任务 5673000: 订单执行器启动
2025/06/12 11:37:17 hedge_task.go:669: 📈 任务 5673000: 下单参数 - 方向: long, 数量: 0.01, 理论价格: 2758.5516, 触发源: price_trigger
2025/06/12 11:37:29 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:29 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:29 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:30 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:31 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 hedge_task.go:497: ⚠️ 任务 5673000: 订单执行器通道满，跳过盘口数据
2025/06/12 11:37:32 main.go:219: 收到停止信号，正在停止所有任务...
2025/06/12 11:37:32 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 11:37:32 manager.go:141: 停止对冲任务: 8336000
2025/06/12 11:37:32 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 11:37:32 manager.go:141: 停止对冲任务: 5673000
2025/06/12 11:37:32 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/12 11:37:32 manager.go:141: 停止对冲任务: 2564000
2025/06/12 11:37:32 hedge_task.go:69: 📢 任务 2564000: 收到停止信号
2025/06/12 11:37:32 orderbook_manager.go:104: 📉 任务 2564000 取消订阅交易对 ETHUSDT (引用计数: 2)
2025/06/12 11:37:32 hedge_task.go:71: 🛑 对冲任务结束: 2564000
2025/06/12 11:37:32 hedge_task.go:69: 📢 任务 8336000: 收到停止信号
2025/06/12 11:37:32 orderbook_manager.go:104: 📉 任务 8336000 取消订阅交易对 ETHUSDT (引用计数: 1)
2025/06/12 11:37:32 hedge_task.go:71: 🛑 对冲任务结束: 8336000
2025/06/12 11:37:32 hedge_task.go:69: 📢 任务 5673000: 收到停止信号
2025/06/12 11:37:32 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/12 11:37:32 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/12 11:37:32 orderbook_manager.go:104: 📉 任务 5673000 取消订阅交易对 ETHUSDT (引用计数: 0)
2025/06/12 11:37:32 orderbook_manager.go:135: 🔌 停止交易对 ETHUSDT 的WebSocket连接
2025/06/12 11:37:32 hedge_task.go:71: 🛑 对冲任务结束: 5673000
2025/06/12 11:37:32 orderbook_manager.go:148: 📊 交易对 ETHUSDT 的数据广播已停止
2025/06/12 11:37:32 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/12 11:37:32 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/12 11:37:32 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/12 11:37:32 data_manager.go:228: 💾 事件数据已保存 (3个事件)
