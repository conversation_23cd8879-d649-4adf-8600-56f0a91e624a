2025/06/11 13:46:14 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/11 13:46:14 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/11 13:46:14 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/11 13:46:14 main.go:190: API接口:
2025/06/11 13:46:14 main.go:191:   POST /task                           - 创建对冲任务
2025/06/11 13:46:14 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/11 13:46:14 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/11 13:46:14 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/11 13:46:14 main.go:195: 期权系统API接口:
2025/06/11 13:46:14 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/11 13:46:14 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/11 13:46:14 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/11 13:46:14 api.go:35: API服务器启动，端口: 5879
2025/06/11 13:46:14 main.go:384: 
2025/06/11 13:46:14 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/11 13:46:14 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/11 13:46:14 main.go:388:       示例: add long 3500 0.0005
2025/06/11 13:46:14 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/11 13:46:14 main.go:390: 
2025/06/11 13:46:14 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/11 13:46:14 main.go:392:    list                                     - 列出所有任务
2025/06/11 13:46:14 main.go:393: 
2025/06/11 13:46:14 main.go:394: ⚙️ 参数配置:
2025/06/11 13:46:14 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/11 13:46:14 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/11 13:46:14 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/11 13:46:14 main.go:398: 
2025/06/11 13:46:14 main.go:399: 📊 状态控制:
2025/06/11 13:46:14 main.go:400:    status                                   - 查看状态输出设置
2025/06/11 13:46:14 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/11 13:46:14 main.go:402:    status off                               - 停止状态输出
2025/06/11 13:46:14 main.go:403: 
2025/06/11 13:46:14 main.go:404: 📋 数据导出:
2025/06/11 13:46:14 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/11 13:46:14 main.go:406: 
2025/06/11 13:46:14 main.go:407: 💾 数据管理:
2025/06/11 13:46:14 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/11 13:46:14 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/11 13:46:14 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/11 13:46:14 main.go:411: 
2025/06/11 13:46:14 main.go:412: 📋 事件查看:
2025/06/11 13:46:14 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/11 13:46:14 main.go:414: 
2025/06/11 13:46:14 main.go:415: 🎛️  批量控制:
2025/06/11 13:46:14 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/11 13:46:14 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/11 13:46:14 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/11 13:46:14 main.go:419: 
2025/06/11 13:46:14 main.go:420: 🗑️  任务管理:
2025/06/11 13:46:14 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/11 13:46:14 main.go:422: 
2025/06/11 13:46:14 main.go:423: 🔧 其他:
2025/06/11 13:46:14 main.go:424:    help                                     - 显示此帮助信息
2025/06/11 13:46:14 main.go:425:    quit | exit                              - 退出程序
2025/06/11 13:46:14 main.go:426: ========================
2025/06/11 13:46:14 main.go:427: 
2025/06/11 13:46:16 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 13:46:16 main.go:723: 
2025/06/11 13:46:16 main.go:824: M207000 停止   L    2750 0.020% 0.100  L0.10@2797            1  13:30  15m   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 13:46:16 main.go:824: M541000 停止   S    2750 0.020% 0.100  无仓位                   0  13:30  15m   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 13:46:16 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 13:46:20 状态变化: stopped -> running | 原因: 批量启动
2025/06/11 13:46:20 状态变化: stopped -> running | 原因: 批量启动
2025/06/11 13:46:20 hedge_task.go:45: 启动对冲任务: M207000
2025/06/11 13:46:20 hedge_task.go:45: 启动对冲任务: M541000
2025/06/11 13:46:20 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/11 13:46:20 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 13:46:21 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 13:46:21 main.go:723: 
2025/06/11 13:46:21 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30  16m   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 13:46:21 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30  16m   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 13:46:21 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 13:51:14 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/11 13:51:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 13:56:14 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/11 13:56:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:01:14 data_manager.go:184: 💾 任务数据已保存 (2个任务)
2025/06/11 14:01:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:01:34 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/11 14:01:34 状态变化:  -> running | 原因: 任务创建
2025/06/11 14:01:34 manager.go:94: 创建对冲任务: M945000, 交易对: ETHUSDT, 方向: long, 目标价: 2800, 来源: options
2025/06/11 14:01:34 api.go:283: API: 期权系统创建任务成功 - M945000 (来源: options)
2025/06/11 14:01:34 hedge_task.go:45: 启动对冲任务: M945000
2025/06/11 14:01:34 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:01:34 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:01:37 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/11 14:01:50 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 14:01:50 main.go:723: 
2025/06/11 14:01:50 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30  31m   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 14:01:50 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30  31m   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 14:01:50 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   0  14:01   0m   0.00     0.00   0.00 是   options  ETH-12JUN25-2800-C-...
2025/06/11 14:01:50 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 14:06:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:06:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:06:23 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 14:06:23 main.go:723: 
2025/06/11 14:06:23 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30  36m   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 14:06:23 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   0  14:01   5m   0.00     0.00   0.00 是   options  ETH-12JUN25-2800-C-...
2025/06/11 14:06:23 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30  36m   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 14:06:23 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 14:07:45 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 14:11:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:11:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:16:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:16:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:21:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:21:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:26:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:26:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:31:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:31:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:36:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:36:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:41:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:41:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:46:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:46:15 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:51:15 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:51:15 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 14:56:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 14:56:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:01:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:01:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:06:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:06:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:11:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:11:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:16:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:16:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:21:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:21:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:26:14 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:26:14 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:28:28 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 15:28:28 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 15:28:28 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 15:32:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:32:52 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:37:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:37:52 data_manager.go:228: 💾 事件数据已保存 (1个事件)
2025/06/11 15:39:53 hedge_task.go:116: 任务 M945000: 触发做多开仓条件，当前价格: 2800.595, 触发价格: 2800.56
2025/06/11 15:39:53 hedge_task.go:151: 任务 M945000: 开始执行下单，类型: open, 方向: long, 触发价格: 2800.56
2025/06/11 15:39:53 hedge_task.go:176: 任务 M945000: 计算理论价格: 2800.56 (事件类型: open, 方向: long)
2025/06/11 15:39:54 hedge_task.go:280: 任务 M945000: 挂买入限价单，价格: 2800.59, 数量: 0.1
2025/06/11 15:39:54 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.1 @ 2800.59, 订单ID: 8389765903300731560, 模式: POST_ONLY
2025/06/11 15:39:57 hedge_task.go:453: 任务 M945000: 订单 8389765903300731560 已成交，价格: 2800.59, 成交方式: maker
2025/06/11 15:39:57 hedge_task.go:204: 任务 M945000: 订单成交，记录事件和更新仓位
2025/06/11 15:39:57 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2800.56 | 实际价: 2800.59 | 损耗: 0.03 | 手续费: 0.0560118 | 订单ID: 8389765903300731560
2025/06/11 15:39:57 hedge_task.go:235: 任务 M945000: 下单完成，实际价格: 2800.59, 理论价格: 2800.56, 价格损耗: 0.03, 订单ID: 8389765903300731560
2025/06/11 15:39:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:39:57 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/11 15:39:57 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:39:57 data_manager.go:228: 💾 事件数据已保存 (2个事件)
2025/06/11 15:40:10 hedge_task.go:135: 任务 M945000: 触发多仓平仓条件，当前价格: 2799.265, 触发价格: 2799.44
2025/06/11 15:40:10 hedge_task.go:151: 任务 M945000: 开始执行下单，类型: close, 方向: short, 触发价格: 2799.44
2025/06/11 15:40:10 hedge_task.go:176: 任务 M945000: 计算理论价格: 2799.44 (事件类型: close, 方向: short)
2025/06/11 15:40:10 hedge_task.go:309: 任务 M945000: 挂卖出限价单，价格: 2799.27, 数量: 0.1
2025/06/11 15:40:10 hedge_task.go:315: 任务 M945000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/11 15:40:10 hedge_task.go:199: 任务 M945000: 下单失败，不更新仓位状态
2025/06/11 15:40:12 hedge_task.go:135: 任务 M945000: 触发多仓平仓条件，当前价格: 2799.345, 触发价格: 2799.44
2025/06/11 15:40:12 hedge_task.go:151: 任务 M945000: 开始执行下单，类型: close, 方向: short, 触发价格: 2799.44
2025/06/11 15:40:12 hedge_task.go:176: 任务 M945000: 计算理论价格: 2799.44 (事件类型: close, 方向: short)
2025/06/11 15:40:15 hedge_task.go:309: 任务 M945000: 挂卖出限价单，价格: 2799.35, 数量: 0.1
2025/06/11 15:40:15 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.1 @ 2799.35, 订单ID: 8389765903300844709, 模式: POST_ONLY
2025/06/11 15:40:20 hedge_task.go:426: 任务 M945000: 订单 8389765903300844709 超时，尝试撤销
2025/06/11 15:40:21 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903300844709
2025/06/11 15:40:21 hedge_task.go:195: 任务 M945000: 订单被撤销，未成交，不更新仓位状态
2025/06/11 15:40:21 hedge_task.go:135: 任务 M945000: 触发多仓平仓条件，当前价格: 2798.895, 触发价格: 2799.44
2025/06/11 15:40:21 hedge_task.go:151: 任务 M945000: 开始执行下单，类型: close, 方向: short, 触发价格: 2799.44
2025/06/11 15:40:21 hedge_task.go:176: 任务 M945000: 计算理论价格: 2799.44 (事件类型: close, 方向: short)
2025/06/11 15:40:23 hedge_task.go:309: 任务 M945000: 挂卖出限价单，价格: 2798.9, 数量: 0.1
2025/06/11 15:40:25 hedge_task.go:315: 任务 M945000: 下单失败: 下限价单失败: <APIError> code=-5022, msg=Due to the order could not be executed as maker, the Post Only order will be rejected. The order will not be recorded in the order history
2025/06/11 15:40:25 hedge_task.go:199: 任务 M945000: 下单失败，不更新仓位状态
2025/06/11 15:40:25 hedge_task.go:135: 任务 M945000: 触发多仓平仓条件，当前价格: 2799.305, 触发价格: 2799.44
2025/06/11 15:40:25 hedge_task.go:151: 任务 M945000: 开始执行下单，类型: close, 方向: short, 触发价格: 2799.44
2025/06/11 15:40:25 hedge_task.go:176: 任务 M945000: 计算理论价格: 2799.44 (事件类型: close, 方向: short)
2025/06/11 15:40:26 hedge_task.go:309: 任务 M945000: 挂卖出限价单，价格: 2799.31, 数量: 0.1
2025/06/11 15:40:26 binance_client.go:95: ✅ 限价单已下达: ETHUSDT SELL 0.1 @ 2799.31, 订单ID: 8389765903300876529, 模式: POST_ONLY
2025/06/11 15:40:27 hedge_task.go:453: 任务 M945000: 订单 8389765903300876529 已成交，价格: 2799.31, 成交方式: maker
2025/06/11 15:40:27 hedge_task.go:204: 任务 M945000: 订单成交，记录事件和更新仓位
2025/06/11 15:40:27 事件类型: close | 订单类型: limit | 成交方式: maker | 理论价: 2799.44 | 实际价: 2799.31 | 损耗: 0.13 | 手续费: 0.0559862 | 订单ID: 8389765903300876529
2025/06/11 15:40:27 hedge_task.go:235: 任务 M945000: 下单完成，实际价格: 2799.31, 理论价格: 2799.44, 价格损耗: 0.13, 订单ID: 8389765903300876529
2025/06/11 15:40:27 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:40:27 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 15:40:27 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:40:27 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 15:40:31 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 15:40:31 main.go:723: 
2025/06/11 15:40:31 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   2  14:01   2h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 15:40:31 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30   2h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 15:40:31 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30   2h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 15:40:31 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 15:42:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:42:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 15:47:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:47:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 15:52:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:52:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 15:57:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 15:57:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:02:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:02:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:07:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:07:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:12:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:12:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:17:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:17:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:22:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:22:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:23:42 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 16:27:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:27:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:32:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:32:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:37:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:37:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:42:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:42:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:47:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:47:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:52:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:52:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 16:57:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 16:57:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:02:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:02:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:05:09 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 17:05:09 main.go:723: 
2025/06/11 17:05:09 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   2  14:01   3h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 17:05:09 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30   4h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 17:05:09 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30   4h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 17:05:09 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 17:07:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:07:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:12:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:12:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:17:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:17:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:22:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:22:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:26:56 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 17:27:01 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 17:27:01 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: websocket: close 1006 (abnormal closure): unexpected EOF, 5秒后重试...
2025/06/11 17:27:52 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:27:52 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:32:19 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/11 17:32:19 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:32:19 manager.go:141: 停止对冲任务: M207000
2025/06/11 17:32:19 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:32:19 manager.go:141: 停止对冲任务: M541000
2025/06/11 17:32:19 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:32:19 hedge_task.go:60: 任务 M207000: 收到停止信号
2025/06/11 17:32:19 hedge_task.go:476: 任务 M207000: 跳过平仓，保留仓位: long 0.1
2025/06/11 17:32:19 hedge_task.go:60: 任务 M541000: 收到停止信号
2025/06/11 17:32:19 hedge_task.go:470: 任务 M541000: 无仓位，直接停止
2025/06/11 17:32:19 hedge_task.go:62: 对冲任务结束: M541000
2025/06/11 17:32:19 manager.go:141: 停止对冲任务: M945000
2025/06/11 17:32:19 hedge_task.go:60: 任务 M945000: 收到停止信号
2025/06/11 17:32:19 hedge_task.go:470: 任务 M945000: 无仓位，直接停止
2025/06/11 17:32:19 hedge_task.go:62: 对冲任务结束: M945000
2025/06/11 17:32:19 hedge_task.go:62: 对冲任务结束: M207000
2025/06/11 17:32:19 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:32:19 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:32:19 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:32:19 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:32:19 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:32:19 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:32:26 main.go:172: 启动API服务模式（包含交互功能），端口: 5879
2025/06/11 17:32:26 main.go:173: 💡 API模式下同时支持所有交互命令，输入 'help' 查看可用命令
2025/06/11 17:32:26 main.go:189: API服务器已启动，访问地址: http://localhost:5879
2025/06/11 17:32:26 main.go:190: API接口:
2025/06/11 17:32:26 main.go:191:   POST /task                           - 创建对冲任务
2025/06/11 17:32:26 main.go:192:   POST /task/stop                      - 停止对冲任务
2025/06/11 17:32:26 main.go:193:   GET  /tasks                          - 获取所有任务
2025/06/11 17:32:26 main.go:194:   GET  /task/{id}                      - 获取单个任务
2025/06/11 17:32:26 main.go:195: 期权系统API接口:
2025/06/11 17:32:26 main.go:196:   POST /api/tasks                      - 创建对冲任务(期权系统)
2025/06/11 17:32:26 main.go:197:   PUT  /api/tasks/{id}/amount          - 更新任务数量
2025/06/11 17:32:26 main.go:198:   GET  /api/tasks/by-option/{contract} - 根据期权合约查询任务
2025/06/11 17:32:26 main.go:384: 
2025/06/11 17:32:26 main.go:385: 🔧 === 交互命令帮助 ===
2025/06/11 17:32:26 main.go:387:    add <long|short> <target_price> <stop_rate> [symbol] [slippage] [stop_slippage] [timeout] [volume]
2025/06/11 17:32:26 main.go:388:       示例: add long 3500 0.0005
2025/06/11 17:32:26 main.go:389:       示例: add short 3500 0.0005 ETHUSDT 0.01 0.01 5 0.5
2025/06/11 17:32:26 main.go:390: 
2025/06/11 17:32:26 main.go:391:    stop <task_id>                           - 停止指定任务
2025/06/11 17:32:26 main.go:392:    list                                     - 列出所有任务
2025/06/11 17:32:26 main.go:393: 
2025/06/11 17:32:26 main.go:394: ⚙️ 参数配置:
2025/06/11 17:32:26 main.go:395:    set <参数名> <值>                         - 设置默认参数
2025/06/11 17:32:26 main.go:396:       参数名: symbol, slippage, stop_slippage, timeout, volume, post_only
2025/06/11 17:32:26 main.go:397:    show defaults                            - 显示当前默认参数
2025/06/11 17:32:26 main.go:398: 
2025/06/11 17:32:26 main.go:399: 📊 状态控制:
2025/06/11 17:32:26 main.go:400:    status                                   - 查看状态输出设置
2025/06/11 17:32:26 main.go:401:    status on [间隔秒数]                     - 启动状态输出（默认10秒）
2025/06/11 17:32:26 main.go:402:    status off                               - 停止状态输出
2025/06/11 17:32:26 main.go:403: 
2025/06/11 17:32:26 main.go:404: 📋 数据导出:
2025/06/11 17:32:26 main.go:405:    export | detail                          - 导出详细状态到剪切板
2025/06/11 17:32:26 main.go:406: 
2025/06/11 17:32:26 api.go:35: API服务器启动，端口: 5879
2025/06/11 17:32:26 main.go:407: 💾 数据管理:
2025/06/11 17:32:26 main.go:408:    save                                     - 手动保存数据到本地文件
2025/06/11 17:32:26 main.go:409:    saveinterval <分钟数>                     - 设置自动保存间隔(默认5分钟)
2025/06/11 17:32:26 main.go:410:    backup [on|off]                          - 控制备份功能(默认关闭)
2025/06/11 17:32:26 main.go:411: 
2025/06/11 17:32:26 main.go:412: 📋 事件查看:
2025/06/11 17:32:26 main.go:413:    events <task_id>                         - 查看指定任务的所有事件日志
2025/06/11 17:32:26 main.go:414: 
2025/06/11 17:32:26 main.go:415: 🎛️  批量控制:
2025/06/11 17:32:26 main.go:416:    startall                                 - 启动所有已停止的任务
2025/06/11 17:32:26 main.go:417:    stopall                                  - 停止所有运行中的任务
2025/06/11 17:32:26 main.go:418:    stopall --skip-close                     - 停止所有任务(跳过平仓)
2025/06/11 17:32:26 main.go:419: 
2025/06/11 17:32:26 main.go:420: 🗑️  任务管理:
2025/06/11 17:32:26 main.go:421:    delete <task_id>                         - 删除指定任务及其所有记录
2025/06/11 17:32:26 main.go:422: 
2025/06/11 17:32:26 main.go:423: 🔧 其他:
2025/06/11 17:32:26 main.go:424:    help                                     - 显示此帮助信息
2025/06/11 17:32:26 main.go:425:    quit | exit                              - 退出程序
2025/06/11 17:32:26 main.go:426: ========================
2025/06/11 17:32:26 main.go:427: 
2025/06/11 17:32:55 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 17:32:55 main.go:723: 
2025/06/11 17:32:55 main.go:824: M945000 停止   L    2800 0.020% 0.100  无仓位                   2  14:01   4h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 17:32:55 main.go:824: M207000 停止   L    2750 0.020% 0.100  L0.10@2797            1  13:30   4h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 17:32:55 main.go:824: M541000 停止   S    2750 0.020% 0.100  无仓位                   0  13:30   4h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 17:32:55 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 17:33:03 状态变化: stopped -> running | 原因: 批量启动
2025/06/11 17:33:03 状态变化: stopped -> running | 原因: 批量启动
2025/06/11 17:33:03 状态变化: stopped -> running | 原因: 批量启动
2025/06/11 17:33:03 hedge_task.go:45: 启动对冲任务: M207000
2025/06/11 17:33:03 hedge_task.go:45: 启动对冲任务: M541000
2025/06/11 17:33:03 hedge_task.go:45: 启动对冲任务: M945000
2025/06/11 17:33:03 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:33:03 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:33:06 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/11 17:36:23 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 17:36:23 main.go:723: 
2025/06/11 17:36:23 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   2  14:01   4h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 17:36:23 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30   4h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 17:36:23 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30   4h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 17:36:23 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 17:37:26 data_manager.go:184: 💾 任务数据已保存 (3个任务)
2025/06/11 17:37:26 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:39:44 api.go:314: API: 期权合约查询完成 - TEST-CONNECTION (存在: false)
2025/06/11 17:40:58 manager.go:199: 📋 应用默认参数: 滑点容忍度=0.0005, 停止滑点=0.0005, 市价超时=5s, 盘口量阈值=0.50, 限价模式=true
2025/06/11 17:40:58 状态变化:  -> running | 原因: 任务创建
2025/06/11 17:40:58 manager.go:94: 创建对冲任务: M266000, 交易对: ETHUSDT, 方向: long, 目标价: 2750, 来源: options
2025/06/11 17:40:58 api.go:283: API: 期权系统创建任务成功 - M266000 (来源: options)
2025/06/11 17:40:58 hedge_task.go:45: 启动对冲任务: M266000
2025/06/11 17:40:58 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:40:58 data_manager.go:228: 💾 事件数据已保存 (3个事件)
2025/06/11 17:41:11 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 17:41:11 main.go:723: 
2025/06/11 17:41:11 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   2  14:01   4h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 17:41:11 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30   4h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 17:41:11 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30   4h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 17:41:11 main.go:824: M266000 运行   L    2750 0.020% 0.100  无仓位                   0  17:40   0m   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-C-...
2025/06/11 17:41:11 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 17:41:16 binance_client.go:209: ❌ WebSocket连接失败: EOF, 5秒后重试...
2025/06/11 17:41:21 hedge_task.go:116: 任务 M266000: 触发做多开仓条件，当前价格: 2765.575, 触发价格: 2750.55
2025/06/11 17:41:21 hedge_task.go:173: 任务 M266000: 执行交易操作 open，方向: long, 基准价格: 2750.55, 理论价格: 2750.55, 数量: 0.1
2025/06/11 17:41:21 hedge_task.go:429: 任务 M266000: 盘口量不足，使用限价单，盘口量比例: 0.0030227918505532
2025/06/11 17:41:22 hedge_task.go:366: 任务 M266000: 挂买入限价单，价格: 2765.57, 数量: 0.1
2025/06/11 17:41:22 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.1 @ 2765.57, 订单ID: 8389765903339490286, 模式: POST_ONLY
2025/06/11 17:41:27 hedge_task.go:512: 任务 M266000: 订单 8389765903339490286 超时，尝试撤销
2025/06/11 17:41:27 binance_client.go:131: ✅ 订单已撤销: ETHUSDT, 订单ID: 8389765903339490286
2025/06/11 17:41:27 hedge_task.go:197: 任务 M266000: 交易操作 open 未成交，订单ID: cancelled_8389765903339490286
2025/06/11 17:41:27 hedge_task.go:116: 任务 M266000: 触发做多开仓条件，当前价格: 2766.055, 触发价格: 2750.55
2025/06/11 17:41:27 hedge_task.go:173: 任务 M266000: 执行交易操作 open，方向: long, 基准价格: 2750.55, 理论价格: 2750.55, 数量: 0.1
2025/06/11 17:41:27 hedge_task.go:429: 任务 M266000: 盘口量不足，使用限价单，盘口量比例: 0.0019760893192372
2025/06/11 17:41:27 hedge_task.go:366: 任务 M266000: 挂买入限价单，价格: 2766.05, 数量: 0.1
2025/06/11 17:41:32 hedge_task.go:372: 任务 M266000: 下单失败: 下限价单失败: <APIError> code=-1021, msg=Timestamp for this request is outside of the recvWindow.
2025/06/11 17:41:32 hedge_task.go:197: 任务 M266000: 交易操作 open 未成交，订单ID: failed_1749634892950747000
2025/06/11 17:41:32 hedge_task.go:116: 任务 M266000: 触发做多开仓条件，当前价格: 2766.595, 触发价格: 2750.55
2025/06/11 17:41:32 hedge_task.go:173: 任务 M266000: 执行交易操作 open，方向: long, 基准价格: 2750.55, 理论价格: 2750.55, 数量: 0.1
2025/06/11 17:41:32 hedge_task.go:429: 任务 M266000: 盘口量不足，使用限价单，盘口量比例: 0.0017676901592689
2025/06/11 17:41:33 hedge_task.go:366: 任务 M266000: 挂买入限价单，价格: 2766.59, 数量: 0.1
2025/06/11 17:41:35 binance_client.go:95: ✅ 限价单已下达: ETHUSDT BUY 0.1 @ 2766.59, 订单ID: 8389765903339557484, 模式: POST_ONLY
2025/06/11 17:41:37 hedge_task.go:539: 任务 M266000: 订单 8389765903339557484 已成交，价格: 2766.59, 成交方式: maker
2025/06/11 17:41:37 事件类型: open | 订单类型: limit | 成交方式: maker | 理论价: 2750.55 | 实际价: 2766.59 | 损耗: 16.04 | 手续费: 0.0553318 | 订单ID: 8389765903339557484
2025/06/11 17:41:37 hedge_task.go:229: 任务 M266000: 交易操作 open 完成，实际价格: 2766.59, 价格损耗: 16.04, 订单ID: 8389765903339557484
2025/06/11 17:41:37 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:41:37 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/06/11 17:41:37 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:41:37 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/06/11 17:41:41 main.go:722: __________________________________________________________________________________________________________________________
2025/06/11 17:41:41 main.go:723: 
2025/06/11 17:41:41 main.go:824: M945000 运行   L    2800 0.020% 0.100  无仓位                   2  14:01   4h   0.02     0.08   0.11 是   options  ETH-12JUN25-2800-C-...
2025/06/11 17:41:41 main.go:824: M207000 运行   L    2750 0.020% 0.100  L0.10@2797            1  13:30   4h   0.00    47.40   0.06 是   options  ETH-11JUN25-2750-C-...
2025/06/11 17:41:41 main.go:824: M266000 运行   L    2750 0.020% 0.100  L0.10@2766            1  17:40   1m   0.00    16.04   0.06 是   options  ETH-12JUN25-2750-C-...
2025/06/11 17:41:41 main.go:824: M541000 运行   S    2750 0.020% 0.100  无仓位                   0  13:30   4h   0.00     0.00   0.00 是   options  ETH-12JUN25-2750-P-...
2025/06/11 17:41:41 main.go:842: __________________________________________________________________________________________________________________________
2025/06/11 17:42:07 main.go:214: 收到停止信号，正在停止所有任务...
2025/06/11 17:42:07 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:42:07 hedge_task.go:60: 任务 M207000: 收到停止信号
2025/06/11 17:42:07 manager.go:141: 停止对冲任务: M207000
2025/06/11 17:42:07 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:42:07 hedge_task.go:562: 任务 M207000: 跳过平仓，保留仓位: long 0.1
2025/06/11 17:42:07 hedge_task.go:62: 对冲任务结束: M207000
2025/06/11 17:42:07 hedge_task.go:60: 任务 M541000: 收到停止信号
2025/06/11 17:42:07 hedge_task.go:556: 任务 M541000: 无仓位，直接停止
2025/06/11 17:42:07 hedge_task.go:62: 对冲任务结束: M541000
2025/06/11 17:42:07 manager.go:141: 停止对冲任务: M541000
2025/06/11 17:42:07 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:42:07 manager.go:141: 停止对冲任务: M945000
2025/06/11 17:42:07 状态变化: running -> stopped | 原因: 手动停止(跳过平仓)
2025/06/11 17:42:07 manager.go:141: 停止对冲任务: M266000
2025/06/11 17:42:07 hedge_task.go:60: 任务 M266000: 收到停止信号
2025/06/11 17:42:07 hedge_task.go:60: 任务 M945000: 收到停止信号
2025/06/11 17:42:07 hedge_task.go:556: 任务 M945000: 无仓位，直接停止
2025/06/11 17:42:07 hedge_task.go:62: 对冲任务结束: M945000
2025/06/11 17:42:07 hedge_task.go:562: 任务 M266000: 跳过平仓，保留仓位: long 0.1
2025/06/11 17:42:07 hedge_task.go:62: 对冲任务结束: M266000
2025/06/11 17:42:07 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:42:07 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/06/11 17:42:07 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:42:07 binance_client.go:209: ❌ WebSocket连接失败: 读取WebSocket数据失败: read tcp 198.18.0.1:53768->198.18.0.45:443: use of closed network connection, 5秒后重试...
2025/06/11 17:42:07 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/06/11 17:42:07 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:42:07 data_manager.go:228: 💾 事件数据已保存 (4个事件)
2025/06/11 17:42:07 data_manager.go:184: 💾 任务数据已保存 (4个任务)
2025/06/11 17:42:07 data_manager.go:228: 💾 事件数据已保存 (4个事件)
