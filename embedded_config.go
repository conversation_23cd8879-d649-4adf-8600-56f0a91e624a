package main

import "github.com/shopspring/decimal"

// EmbeddedConfig 内嵌配置结构
type EmbeddedConfig struct {
	// 币安API配置
	Binance struct {
		ApiKey    string
		SecretKey string
	}

	// 交易配置
	Trading struct {
		MakerFeeRate decimal.Decimal // 挂单费率 0.02% (Maker)
		TakerFeeRate decimal.Decimal // 吃单费率 0.04% (Taker)
	}

	// 服务器配置
	Server struct {
		Port int
		Host string
	}

	// 日志配置
	Logging struct {
		Level      string // debug, info, warn, error
		File       string
		MaxSize    int // MB
		MaxBackups int
		MaxAge     int // days
	}

	// 默认交易参数
	Defaults struct {
		SlippageTolerance decimal.Decimal // 滑点容忍度
		StopSlippage      decimal.Decimal // 止损滑点
		MarketTimeout     int             // 市价单超时时间(秒)
		VolumeThreshold   decimal.Decimal // 成交量阈值
	}

	// 风险控制
	Risk struct {
		MaxTasks        int             // 最大并发任务数
		MaxPositionSize decimal.Decimal // 最大单个仓位大小
		DailyLossLimit  decimal.Decimal // 日损失限制 (USDT)
	}

	// 监控配置
	Monitoring struct {
		StatusInterval      int // 状态输出间隔 (秒)
		HealthCheckInterval int // 健康检查间隔 (秒)
	}
}

// GetEmbeddedConfig 返回内嵌的配置
func GetEmbeddedConfig() *EmbeddedConfig {
	config := &EmbeddedConfig{}

	// 币安API配置 - 在这里填写你的真实API密钥
	config.Binance.ApiKey = "cNnJC8hzGWl0Lho4qtL8lIVI2S0U3qvNYoDum9Q4gMIT7EVQDUJxPTqEVwZvbRyJ"
	config.Binance.SecretKey = "1YlxoJ8O7mWb0EvfAG457yALdgMiyG4bNFnmc8PaudTRI9aPHAlHoHWoPyJA0yqP"

	// 交易配置
	config.Trading.MakerFeeRate = decimal.NewFromFloat(0.0002) // 0.02%
	config.Trading.TakerFeeRate = decimal.NewFromFloat(0.0004) // 0.04%

	// 服务器配置
	config.Server.Port = 5879
	config.Server.Host = "localhost"

	// 日志配置
	config.Logging.Level = "info"
	config.Logging.File = "hedge.log"
	config.Logging.MaxSize = 100
	config.Logging.MaxBackups = 3
	config.Logging.MaxAge = 28

	// 默认交易参数
	config.Defaults.SlippageTolerance = decimal.NewFromFloat(0.0005) // 0.05%
	config.Defaults.StopSlippage = decimal.NewFromFloat(0.0005)      // 0.05%
	config.Defaults.MarketTimeout = 5                                // 5秒
	config.Defaults.VolumeThreshold = decimal.NewFromFloat(0.5)      // 50%

	// 风险控制
	config.Risk.MaxTasks = 20
	config.Risk.MaxPositionSize = decimal.NewFromFloat(1.0)   // 1.0 ETH
	config.Risk.DailyLossLimit = decimal.NewFromFloat(1000.0) // 1000 USDT

	// 监控配置
	config.Monitoring.StatusInterval = 10      // 10秒
	config.Monitoring.HealthCheckInterval = 30 // 30秒

	return config
}

// ConvertToConfig 将内嵌配置转换为原有的Config结构
func (ec *EmbeddedConfig) ConvertToConfig() *Config {
	return &Config{
		Binance: struct {
			APIKey    string `yaml:"api_key"`
			SecretKey string `yaml:"secret_key"`
		}{
			APIKey:    ec.Binance.ApiKey,
			SecretKey: ec.Binance.SecretKey,
		},
		Trading: struct {
			MakerFeeRate float64 `yaml:"maker_fee_rate"`
			TakerFeeRate float64 `yaml:"taker_fee_rate"`
		}{
			MakerFeeRate: ec.Trading.MakerFeeRate.InexactFloat64(),
			TakerFeeRate: ec.Trading.TakerFeeRate.InexactFloat64(),
		},
		Server: struct {
			Port int `yaml:"port"`
		}{
			Port: ec.Server.Port,
		},
		Logging: struct {
			Level string `yaml:"level"`
		}{
			Level: ec.Logging.Level,
		},
	}
}
