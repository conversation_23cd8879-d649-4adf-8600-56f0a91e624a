package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// DataManager 数据管理器
type DataManager struct {
	dataDir       string
	tasksFile     string
	eventsFile    string
	backupDir     string
	mutex         sync.RWMutex
	autoSave      bool
	saveChannel   chan struct{}
	saveInterval  time.Duration
	backupEnabled bool
}

// TaskData 任务数据结构（用于序列化）
type TaskData struct {
	Tasks map[string]*HedgeTask `json:"tasks"`
	Meta  DataMeta              `json:"meta"`
}

// EventData 事件数据结构（用于序列化）
type EventData struct {
	Events map[string][]Event `json:"events"` // key为任务ID，value为该任务的事件列表
	Meta   DataMeta           `json:"meta"`
}

// DataMeta 数据元信息
type DataMeta struct {
	Version   string    `json:"version"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	TaskCount int       `json:"task_count"`
}

// NewDataManager 创建新的数据管理器
func NewDataManager() *DataManager {
	dataDir := "data"
	dm := &DataManager{
		dataDir:       dataDir,
		tasksFile:     filepath.Join(dataDir, "tasks.json"),
		eventsFile:    filepath.Join(dataDir, "events.json"),
		backupDir:     filepath.Join(dataDir, "backup"),
		autoSave:      true,
		saveChannel:   make(chan struct{}, 100), // 缓冲通道，避免阻塞
		saveInterval:  5 * time.Minute,          // 默认5分钟自动保存
		backupEnabled: false,                    // 默认关闭备份
	}

	// 确保数据目录存在
	if err := dm.ensureDirectories(); err != nil {
		log.Printf("❌ 创建数据目录失败: %v", err)
	}

	return dm
}

// ensureDirectories 确保必要的目录存在
func (dm *DataManager) ensureDirectories() error {
	dirs := []string{dm.dataDir, dm.backupDir}
	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录 %s 失败: %v", dir, err)
		}
	}
	return nil
}

// LoadTasks 加载任务数据
func (dm *DataManager) LoadTasks() (map[string]*HedgeTask, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查文件是否存在
	if _, err := os.Stat(dm.tasksFile); os.IsNotExist(err) {
		log.Println("📄 任务数据文件不存在，返回空任务列表")
		return make(map[string]*HedgeTask), nil
	}

	// 读取文件
	data, err := os.ReadFile(dm.tasksFile)
	if err != nil {
		return nil, fmt.Errorf("读取任务文件失败: %v", err)
	}

	// 解析JSON
	var taskData TaskData
	if err := json.Unmarshal(data, &taskData); err != nil {
		return nil, fmt.Errorf("解析任务数据失败: %v", err)
	}

	// 恢复任务的运行时状态
	for _, task := range taskData.Tasks {
		// 重新初始化不能序列化的字段
		task.StopChan = make(chan struct{})
		task.mutex = sync.RWMutex{}

		// 如果任务之前是运行状态，现在设为停止状态（需要手动重启）
		if task.Status == StatusRunning {
			task.Status = StatusStopped
		}

		// 初始化统计信息（如果为空）
		if task.Statistics == nil {
			task.Statistics = &Stats{}
		}
	}

	log.Printf("✅ 成功加载 %d 个任务", len(taskData.Tasks))
	return taskData.Tasks, nil
}

// LoadEvents 加载事件数据
func (dm *DataManager) LoadEvents() (map[string][]Event, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 检查文件是否存在
	if _, err := os.Stat(dm.eventsFile); os.IsNotExist(err) {
		log.Println("📄 事件数据文件不存在，返回空事件列表")
		return make(map[string][]Event), nil
	}

	// 读取文件
	data, err := os.ReadFile(dm.eventsFile)
	if err != nil {
		return nil, fmt.Errorf("读取事件文件失败: %v", err)
	}

	// 解析JSON
	var eventData EventData
	if err := json.Unmarshal(data, &eventData); err != nil {
		return nil, fmt.Errorf("解析事件数据失败: %v", err)
	}

	log.Printf("✅ 成功加载 %d 个任务的事件数据", len(eventData.Events))
	return eventData.Events, nil
}

// SaveTasks 保存任务数据
func (dm *DataManager) SaveTasks(tasks map[string]*HedgeTask) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 备份现有文件（如果启用备份）
	if dm.backupEnabled {
		if err := dm.backupFile(dm.tasksFile); err != nil {
			log.Printf("⚠️  备份任务文件失败: %v", err)
		}
	}

	// 准备数据
	taskData := TaskData{
		Tasks: tasks,
		Meta: DataMeta{
			Version:   "1.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			TaskCount: len(tasks),
		},
	}

	// 序列化
	data, err := json.MarshalIndent(taskData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(dm.tasksFile, data, 0644); err != nil {
		return fmt.Errorf("写入任务文件失败: %v", err)
	}

	log.Printf("💾 任务数据已保存 (%d个任务)", len(tasks))
	return nil
}

// SaveEvents 保存事件数据
func (dm *DataManager) SaveEvents(events map[string][]Event) error {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	// 备份现有文件（如果启用备份）
	if dm.backupEnabled {
		if err := dm.backupFile(dm.eventsFile); err != nil {
			log.Printf("⚠️  备份事件文件失败: %v", err)
		}
	}

	// 计算总事件数
	totalEvents := 0
	for _, taskEvents := range events {
		totalEvents += len(taskEvents)
	}

	// 准备数据
	eventData := EventData{
		Events: events,
		Meta: DataMeta{
			Version:   "1.0",
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			TaskCount: totalEvents,
		},
	}

	// 序列化
	data, err := json.MarshalIndent(eventData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化事件数据失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(dm.eventsFile, data, 0644); err != nil {
		return fmt.Errorf("写入事件文件失败: %v", err)
	}

	log.Printf("💾 事件数据已保存 (%d个事件)", totalEvents)
	return nil
}

// backupFile 备份文件
func (dm *DataManager) backupFile(filePath string) error {
	// 检查源文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil // 文件不存在，无需备份
	}

	// 生成备份文件名
	fileName := filepath.Base(filePath)
	backupName := fmt.Sprintf("%s.%s.bak", fileName, time.Now().Format("20060102_150405"))
	backupPath := filepath.Join(dm.backupDir, backupName)

	// 读取源文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取源文件失败: %v", err)
	}

	// 写入备份文件
	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	return nil
}

// StartAutoSave 启动自动保存
func (dm *DataManager) StartAutoSave(taskManager *TaskManager) {
	if !dm.autoSave {
		return
	}

	go func() {
		ticker := time.NewTicker(dm.saveInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 定时自动保存
				dm.saveAllData(taskManager)
			case <-dm.saveChannel:
				// 手动触发保存
				dm.saveAllData(taskManager)
			}
		}
	}()

	log.Printf("🔄 自动保存已启动 (%v间隔)", dm.saveInterval)
}

// SetSaveInterval 设置自动保存间隔
func (dm *DataManager) SetSaveInterval(interval time.Duration) {
	dm.saveInterval = interval
	log.Printf("🔄 自动保存间隔已设置为: %v", interval)
}

// EnableBackup 启用备份功能
func (dm *DataManager) EnableBackup() {
	dm.backupEnabled = true
	log.Println("✅ 备份功能已启用")
}

// DisableBackup 禁用备份功能
func (dm *DataManager) DisableBackup() {
	dm.backupEnabled = false
	log.Println("❌ 备份功能已禁用")
}

// IsBackupEnabled 检查备份功能是否启用
func (dm *DataManager) IsBackupEnabled() bool {
	return dm.backupEnabled
}

// TriggerSave 触发保存
func (dm *DataManager) TriggerSave() {
	select {
	case dm.saveChannel <- struct{}{}:
		// 保存信号已发送
	default:
		// 通道满了，忽略
	}
}

// saveAllData 保存所有数据
func (dm *DataManager) saveAllData(taskManager *TaskManager) {
	// 获取所有任务
	tasks := taskManager.GetAllTasks()

	// 提取事件数据
	events := make(map[string][]Event)
	for taskID, task := range tasks {
		task.mutex.RLock()
		events[taskID] = make([]Event, len(task.Events))
		copy(events[taskID], task.Events)
		task.mutex.RUnlock()
	}

	// 保存任务数据
	if err := dm.SaveTasks(tasks); err != nil {
		log.Printf("❌ 保存任务数据失败: %v", err)
	}

	// 保存事件数据
	if err := dm.SaveEvents(events); err != nil {
		log.Printf("❌ 保存事件数据失败: %v", err)
	}
}

// CleanupOldBackups 清理旧备份文件（保留最近10个）
func (dm *DataManager) CleanupOldBackups() error {
	files, err := os.ReadDir(dm.backupDir)
	if err != nil {
		return fmt.Errorf("读取备份目录失败: %v", err)
	}

	// 按修改时间排序，删除最旧的文件
	if len(files) > 10 {
		// 这里可以实现更复杂的清理逻辑
		log.Printf("📁 备份文件数量: %d，建议定期清理", len(files))
	}

	return nil
}
