
## 重新整理的详细完整修改方案

### 1. 公共盘口数据管理系统

#### 1.1 盘口管理器（OrderBookManager）
- **全局单例**：创建全局的盘口管理器实例
- **交易对管理**：维护所有需要订阅的交易对列表
- **WebSocket连接**：为每个交易对维护独立的WebSocket连接
- **数据缓存**：缓存每个交易对的最新盘口数据（买一价、买一量、卖一价、卖一量）
- **广播机制**：盘口数据更新时，广播给所有订阅该交易对的任务

#### 1.2 订阅管理
- **注册订阅**：任务启动时向盘口管理器注册需要的交易对
- **引用计数**：记录每个交易对的订阅任务数量
- **自动清理**：当某个交易对没有任务订阅时，断开该交易对的WebSocket连接
- **取消订阅**：任务停止时自动取消订阅

#### 1.3 连接维护
- **心跳机制**：定期发送ping消息保持连接活跃
- **自动重连**：连接断开时自动重连，重连成功后恢复所有订阅
- **健康检查**：监控数据更新频率，长时间无数据时触发重连

### 2. 三仓位状态管理系统

#### 2.1 三仓位状态结构
每个任务维护三套仓位信息：

**设定仓位（ConfiguredPosition）**：
- `ConfiguredSide`：设定的仓位方向（long/short）
- `ConfiguredSize`：设定的仓位大小
- `LastUpdateTime`：最后更新时间
- `UpdateSource`：更新来源（initial/api_adjustment）

**应有仓位（RequiredPosition）**：
- `RequiredSide`：根据市价和阈值计算的理论仓位方向（long/short/none）
- `RequiredSize`：根据市价和阈值计算的理论仓位大小
- `LastCalculateTime`：最后计算时间
- `CalculatePrice`：计算时使用的市价

**现有仓位（CurrentPosition）**：
- `CurrentSide`：当前实际仓位方向（long/short/none）
- `CurrentSize`：当前实际仓位大小

#### 2.2 应有仓位计算逻辑

**做多对冲任务**：
- 当买一价 > 目标价格 + (目标价格 × 阈值) 时：
  - 应有仓位 = 设定仓位（方向：long，大小：设定大小）
- 当卖一价 < 目标价格 - (目标价格 × 阈值) 时：
  - 应有仓位 = 空仓（方向：none，大小：0）
- 其他情况：
  - 应有仓位 = 空仓（方向：none，大小：0）

**做空对冲任务**：
- 当卖一价 < 目标价格 - (目标价格 × 阈值) 时：
  - 应有仓位 = 设定仓位（方向：short，大小：设定大小）
- 当买一价 > 目标价格 + (目标价格 × 阈值) 时：
  - 应有仓位 = 空仓（方向：none，大小：0）
- 其他情况：
  - 应有仓位 = 空仓（方向：none，大小：0）

#### 2.3 仓位不一致检测
- **检测时机**：每次盘口数据更新时
- **检测逻辑**：比较应有仓位与现有仓位
- **触发条件**：应有仓位的方向或大小与现有仓位不一致

### 3. 触发机制重构

#### 3.1 统一触发流程
1. **接收盘口数据**：从盘口管理器接收最新盘口数据
2. **计算应有仓位**：根据盘口价格和阈值计算应有仓位
3. **检测仓位差异**：比较应有仓位与现有仓位
4. **触发下单操作**：如有差异且无正在进行的下单，触发下单

#### 3.2 API调用触发
1. **更新设定仓位**：API调用直接修改设定仓位
2. **重新计算应有仓位**：基于当前盘口数据重新计算应有仓位
3. **立即检测差异**：触发仓位不一致检测
4. **返回API响应**：立即返回，不等待实际交易

### 4. API接口内部实现调整

#### 4.1 保持现有API格式
- **接口路径**：保持现有的API路径不变
- **请求参数**：保持现有的请求参数格式不变
- **响应格式**：保持现有的响应格式不变

#### 4.2 内部实现调整
- **接收API请求**：解析新的目标数量和更新类型
- **更新设定仓位**：将新数量更新到设定仓位状态
- **重新计算应有仓位**：基于当前盘口立即重新计算应有仓位
- **触发检测**：立即触发仓位不一致检测
- **返回响应**：立即返回API调用结果

### 5. 统一下单逻辑

#### 5.1 下单触发统一
所有下单操作都通过仓位不一致检测触发：
- **正常开平仓**：盘口变化 → 重新计算应有仓位 → 仓位不一致 → 下单
- **API调仓**：API调用 → 更新设定仓位 → 重新计算应有仓位 → 仓位不一致 → 下单
- **停止平仓**：停止信号 → 设定仓位设为0 → 重新计算应有仓位 → 仓位不一致 → 下单

#### 5.2 理论价格和滑点计算

**理论价格计算（根据触发源区分）**：

**price_trigger触发源**：
- **做多开仓**：理论价格 = 目标价格 + (目标价格 × 阈值)
- **做多平仓**：理论价格 = 目标价格 - (目标价格 × 阈值)
- **做空开仓**：理论价格 = 目标价格 - (目标价格 × 阈值)
- **做空平仓**：理论价格 = 目标价格 + (目标价格 × 阈值)

**api_adjustment触发源**：
- **加仓操作**：根据加仓方向选择盘口价格
  - 买入加仓：理论价格 = 买一价
  - 卖出加仓：理论价格 = 卖一价
- **减仓操作**：根据减仓方向选择盘口价格
  - 买入减仓：理论价格 = 买一价
  - 卖出减仓：理论价格 = 卖一价

**stop_close触发源**：
- **平仓操作**：根据平仓方向选择盘口价格
  - 买入平仓：理论价格 = 买一价
  - 卖出平仓：理论价格 = 卖一价

**滑点选择**：
- **price_trigger触发**：使用SlippageTolerance（动态对冲滑点容忍度）
- **api_adjustment触发**：使用StopSlippage（停止平仓滑点容忍度）
- **stop_close触发**：使用StopSlippage（停止平仓滑点容忍度）
- **参数来源**：任务创建时设置，未指定时使用全局默认值

#### 5.3 动态挂单详细流程

##### 5.3.1 初始挂单流程

**步骤1：获取下单锁**
- 设置`isOrderInProgress`为true（任务级别锁）
- 检查锁获取是否成功，失败则退出

**步骤2：确定挂单价格**
- **直接使用理论价格**：初始挂单价格 = 理论价格
- **不考虑滑点调整**：首次挂单以最优价格为目标

**步骤3：执行挂单**
- 调用币安API下限价单
- **下单失败处理**：如果下单失败，设置`isOrderInProgress`为false，直接结束下单任务，不更新仓位状态
- **下单成功处理**：记录返回的订单ID、当前挂单价格、挂单时间和订单方向

**步骤4：初始化监控状态**
- 设置`isMonitoringOrderBook`为true
- 开始监控对应的盘口价格变化
- 设置超时计时器
- 初始化订单修改次数计数器为0

##### 5.3.2 动态调整监控流程

**监控触发条件**：
- **买单监控**：只监控买一价变化
  - 买一价发生变化 AND 买一价 ≠ 当前挂单价 → 触发调整检查
- **卖单监控**：只监控卖一价变化
  - 卖一价发生变化 AND 卖一价 ≠ 当前挂单价 → 触发调整检查

**调整决策逻辑**：
1. 获取最新的对应盘口价格（买一价或卖一价）
2. 检查新价格是否在滑点容忍范围内：
   - 买单：新买一价 ≤ 理论价格 × (1 + 滑点容忍度)
   - 卖单：新卖一价 ≥ 理论价格 × (1 - 滑点容忍度)
3. 如果在范围内：执行价格调整
4. 如果超出范围：尝试市价成交

##### 5.3.3 订单价格调整流程

**步骤1：调用修改订单API**
- 使用币安修改订单API
- 传入：订单ID、新价格、原数量
- 记录修改尝试时间
- 订单修改次数计数器+1

**步骤2：处理修改结果**
- **修改成功**：
  - 更新当前挂单价格记录
  - 继续监控盘口变化
  
- **修改失败**：
  - 立即触发订单状态查询流程

##### 5.3.4 订单状态查询流程

**查询触发条件**（仅限以下两种情况）：
- 修改订单API返回失败
- 订单超时未成交

**查询处理逻辑**：
1. **调用币安查询订单状态API**
2. **根据订单状态处理**：
   
   **已成交（FILLED）**：
   - 获取实际成交价格和数量
   - 计算手续费和成交方式
   - 更新现有仓位状态
   - 记录成交事件（包含订单修改次数）
   - 设置`isOrderInProgress`和`isMonitoringOrderBook`为false
   - 结束本次下单任务
   
   **已撤销（CANCELED）**：
   - 不更新仓位状态
   - 记录撤销事件（包含订单修改次数）
   - 设置`isOrderInProgress`和`isMonitoringOrderBook`为false
   - 结束本次下单任务
   
   **不存在或其他异常状态**：
   - 不更新仓位状态
   - 记录异常事件（包含订单修改次数）
   - 设置`isOrderInProgress`和`isMonitoringOrderBook`为false
   - 结束本次下单任务
   
   **仍在挂单（NEW/PARTIALLY_FILLED）**：
   - 继续监控盘口变化
   - 重新开始动态调整流程

##### 5.3.5 市价成交流程

**触发条件**：
- 盘口价格超出滑点容忍范围

**处理流程**：
1. **撤销当前限价单**：调用币安撤单API
2. **下市价单**：立即下市价单
3. **等待成交确认**：查询市价单状态
4. **更新仓位状态**：成交后更新现有仓位
5. **记录事件**：记录市价成交事件（包含之前的订单修改次数）
6. **结束任务**：设置`isOrderInProgress`和`isMonitoringOrderBook`为false

##### 5.3.6 超时处理流程

**超时设置**：
- 使用任务的MarketTimeout参数
- 从初始挂单开始计时

**超时处理**：
1. **停止盘口监控**：设置`isMonitoringOrderBook`为false
2. **执行订单状态查询流程**（同5.3.4）
3. **不执行主动撤单**：让查询结果决定后续处理

### 6. 并发控制机制

#### 6.1 任务级别锁
- **锁的实现**：通过`isOrderInProgress`布尔值实现
- **锁定范围**：从开始下单到订单状态确认完成的整个过程
- **锁定时机**：仓位不一致检测触发下单时，首先获取锁（设置`isOrderInProgress`为true）
- **释放时机**：订单成交、撤销或异常结束时释放锁（设置`isOrderInProgress`为false）

#### 6.2 状态标记
- **下单状态标记**：`isOrderInProgress`（同时作为任务级别锁）
- **当前订单信息**：`currentOrderID`、`currentOrderPrice`、`currentOrderSide`
- **监控状态**：`isMonitoringOrderBook`

#### 6.3 忽略机制
- **并发触发忽略**：`isOrderInProgress`为true时，忽略所有新的仓位不一致触发
- **状态同步**：下单任务结束后，立即基于最新盘口重新检测仓位状态

### 7. 任务生命周期管理

#### 7.1 任务启动
1. 初始化三仓位状态：
   - 设定仓位：根据任务参数设置初始方向和大小
   - 应有仓位：初始为空，等待首次计算
   - 现有仓位：初始为空
2. 向盘口管理器注册交易对订阅
3. 启动任务主循环
4. 接收首次盘口数据并计算应有仓位

#### 7.2 任务运行
1. 接收盘口数据广播
2. 重新计算应有仓位
3. 检测仓位不一致情况
4. 执行下单操作（如需要且无并发冲突）
5. 订单成交后更新现有仓位

#### 7.3 任务停止
1. 接收停止信号
2. 根据停止选项处理：
   - 跳过平仓：直接停止，保持现有仓位不变
   - 执行平仓：将设定仓位设为0，触发平仓操作
3. **等待当前下单操作完成**：
   - 如果有正在进行的下单，等待其完成（成交或失败）
   - 不强制撤销正在进行的订单
4. 取消盘口订阅
5. 清理资源

### 8. 数据结构调整

#### 8.1 任务结构
- **移除**：原有的单一`Position`字段
- **添加**：`ConfiguredPosition`、`RequiredPosition`、`CurrentPosition`三个仓位字段
- **添加订单监控字段**：
  - `currentOrderID`：当前订单ID
  - `currentOrderPrice`：当前挂单价格
  - `currentOrderSide`：当前订单方向（buy/sell）
  - `orderStartTime`：订单开始时间
  - `isOrderInProgress`：是否正在进行下单（任务级别锁）
  - `isMonitoringOrderBook`：是否正在监控盘口
  - `orderModifyCount`：当前订单的修改次数
- **调整**：滑点参数支持任务级别设置和全局默认值

#### 8.2 事件记录
- **触发源**：记录事件的触发来源（price_trigger/api_adjustment/stop_close）
- **仓位状态**：记录事件发生时的三个仓位状态
- **价格信息**：记录触发时的盘口价格信息和理论价格
- **调整详情**：记录仓位调整的具体数量和方向
- **订单修改次数**：记录每次下单过程中的订单修改次数
- **成交方式**：记录最终成交方式（限价/市价）

这个修改后的方案是否准确反映了您关于`isOrderInProgress`设置时机的要求？