#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态对冲系统 - 实盘测试脚本
使用币安API进行真实交易测试
"""

import requests
import json
import time
import sys
from datetime import datetime

# API服务地址
BASE_URL = "http://localhost:5879"

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🔴 动态对冲系统 - 实盘交易测试")
    print("=" * 60)
    print("⚠️  警告：这将使用真实资金进行交易！")
    print("📝 建议：先在测试网验证，再用小额资金测试")
    print("=" * 60)
    print()

def get_current_price(symbol="ETHUSDT"):
    """获取当前价格（用于设置合理的目标价格）"""
    try:
        # 这里可以调用币安API获取当前价格
        # 暂时返回一个示例价格
        return 2650.0
    except Exception as e:
        print(f"❌ 获取价格失败: {e}")
        return 2650.0

def create_real_task():
    """创建实盘测试任务"""
    print("📊 创建实盘交易任务")
    print("-" * 30)
    
    # 获取当前价格作为参考
    current_price = get_current_price()
    print(f"💰 当前ETH价格参考: ${current_price:.2f}")
    
    # 任务参数（小额测试）
    task_data = {
        "symbol": "ETHUSDT",           # 交易对
        "direction": "long",           # 方向：long做多对冲 / short做空对冲
        "target_price": current_price, # 目标价格（当前价格）
        "stop_rate": 0.2,           # 对冲阈值 0.2%（价格变动0.2%时触发）
        "amount": 0.001,              # 交易数量（0.001 ETH，约3-4美元）
        "slippage_tolerance": 0.001,   # 滑点容忍度 0.1%
        "volume_threshold": 0.5,       # 盘口量阈值 50%
        "market_timeout": 10           # 市价超时时间 10秒
    }
    
    print("📋 任务参数:")
    print(f"   交易对: {task_data['symbol']}")
    print(f"   方向: {task_data['direction']} (做多对冲)")
    print(f"   目标价格: ${task_data['target_price']:.2f}")
    print(f"   对冲阈值: {task_data['stop_rate']*100:.1f}%")
    print(f"   交易数量: {task_data['amount']} ETH")
    print(f"   滑点容忍: {task_data['slippage_tolerance']*100:.1f}%")
    print()
    
    # 计算触发价格
    upper_trigger = task_data['target_price'] * (1 + task_data['stop_rate'])
    lower_trigger = task_data['target_price'] * (1 - task_data['stop_rate'])
    
    print("🎯 触发条件:")
    print(f"   开多仓: 价格 > ${upper_trigger:.2f}")
    print(f"   平多仓: 价格 < ${lower_trigger:.2f}")
    print()
    
    # 风险提醒
    estimated_value = task_data['amount'] * task_data['target_price']
    print(f"💸 预估交易金额: ~${estimated_value:.2f}")
    print("⚠️  风险提醒: 这是真实交易，可能产生盈亏")
    print()
    
    # 确认继续
    confirm = input("🤔 确认创建实盘交易任务? (输入 'YES' 继续): ")
    if confirm != "YES":
        print("❌ 已取消")
        return None
    
    try:
        response = requests.post(f"{BASE_URL}/task", json=task_data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            task_id = result['task_id']
            print(f"✅ 任务创建成功!")
            print(f"📋 任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ 任务创建失败: {response.text}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        print("💡 请确保API服务正在运行: ./hedge-system --api --port=5879")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    try:
        response = requests.get(f"{BASE_URL}/tasks", timeout=5)
        if response.status_code == 200:
            tasks = response.json()
            for task in tasks:
                if task['id'] == task_id:
                    return task
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 获取任务状态失败: {e}")
        return None

def print_task_status(task):
    """打印任务状态"""
    if not task:
        print("❌ 无法获取任务状态")
        return
    
    print(f"\n📊 任务状态 [{datetime.now().strftime('%H:%M:%S')}]")
    print("-" * 40)
    print(f"状态: {task['status']}")
    print(f"交易对: {task['symbol']}")
    print(f"方向: {task['direction']}")
    print(f"目标价: ${task['target_price']:.2f}")
    print(f"对冲阈值: {task['stop_rate']*100:.1f}%")
    
    # 仓位信息
    if task.get('position'):
        pos = task['position']
        print(f"当前仓位: {pos['side']} {pos['size']} @ ${pos['entry_price']:.2f}")
    else:
        print("当前仓位: 无")
    
    # 统计信息
    stats = task.get('statistics', {})
    print(f"交易次数: {stats.get('total_trades', 0)}")
    print(f"总盈亏: ${stats.get('total_pnl', 0):.4f}")
    print(f"总手续费: ${stats.get('total_fees', 0):.4f}")
    print(f"价格损耗: ${stats.get('total_price_loss', 0):.4f}")
    
    # 事件数量
    events_count = len(task.get('events', []))
    print(f"事件数量: {events_count}")

def stop_task(task_id):
    """停止任务"""
    try:
        response = requests.post(f"{BASE_URL}/task/stop", json={"task_id": task_id}, timeout=10)
        if response.status_code == 200:
            print(f"✅ 任务停止请求已发送")
            print("⏳ 等待平仓完成...")
            return True
        else:
            print(f"❌ 停止任务失败: {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False

def monitor_task(task_id):
    """监控任务状态"""
    print("\n🔍 开始监控任务状态...")
    print("💡 按 Ctrl+C 停止监控并停止任务")
    print("-" * 50)
    
    try:
        while True:
            task = get_task_status(task_id)
            if task:
                print_task_status(task)
                
                # 检查任务是否已停止
                if task['status'] == 'stopped':
                    print("\n✅ 任务已停止")
                    break
            else:
                print("❌ 无法获取任务状态")
            
            # 等待5秒后再次查询
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n\n🛑 收到停止信号，正在停止任务...")
        if stop_task(task_id):
            # 等待任务停止
            for i in range(30):  # 最多等待30秒
                time.sleep(1)
                task = get_task_status(task_id)
                if task and task['status'] == 'stopped':
                    print("✅ 任务已成功停止")
                    break
                print(f"⏳ 等待停止... ({i+1}/30)")
            else:
                print("⚠️ 任务停止超时，请手动检查")
        
        # 显示最终状态
        final_task = get_task_status(task_id)
        if final_task:
            print("\n📊 最终状态:")
            print_task_status(final_task)

def main():
    """主函数"""
    print_banner()
    
    # 检查API服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/tasks", timeout=5)
        if response.status_code != 200:
            print("❌ API服务未正常响应")
            print("💡 请先启动API服务: ./hedge-system --api --port=5879")
            sys.exit(1)
    except requests.exceptions.RequestException:
        print("❌ 无法连接到API服务")
        print("💡 请先启动API服务: ./hedge-system --api --port=5879")
        sys.exit(1)
    
    print("✅ API服务连接正常")
    print()
    
    # 创建任务
    task_id = create_real_task()
    if not task_id:
        print("❌ 任务创建失败，退出")
        sys.exit(1)
    
    # 监控任务
    monitor_task(task_id)
    
    print("\n🎉 实盘交易测试完成")
    print("📝 请检查交易记录和账户状态")
    print("💡 如有问题，请查看系统日志")

if __name__ == "__main__":
    main() 