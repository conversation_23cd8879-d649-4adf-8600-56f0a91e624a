package main

import (
	"log"
	"sync"
)

// OrderBookManager 全局盘口管理器
type OrderBookManager struct {
	// 订阅管理
	subscriptions map[string]map[string]chan *OrderBookData // symbol -> taskID -> channel
	refCounts     map[string]int                            // symbol -> 引用计数

	// 数据缓存
	latestData map[string]*OrderBookData // symbol -> 最新盘口数据

	// WebSocket连接管理
	connections map[string]chan struct{} // symbol -> 停止信号

	// 并发控制
	mutex sync.RWMutex

	// 币安客户端引用
	binanceClient *BinanceClient
}

// 全局盘口管理器实例
var globalOrderBookManager *OrderBookManager
var orderBookManagerOnce sync.Once

// GetOrderBookManager 获取全局盘口管理器实例（单例模式）
func GetOrderBookManager() *OrderBookManager {
	orderBookManagerOnce.Do(func() {
		globalOrderBookManager = &OrderBookManager{
			subscriptions: make(map[string]map[string]chan *OrderBookData),
			refCounts:     make(map[string]int),
			latestData:    make(map[string]*OrderBookData),
			connections:   make(map[string]chan struct{}),
			binanceClient: binanceClient, // 使用全局币安客户端
		}
		log.Println("📊 全局盘口管理器已初始化")
	})
	return globalOrderBookManager
}

// Subscribe 订阅交易对盘口数据
// taskID: 任务ID，symbol: 交易对，dataChan: 接收盘口数据的通道
func (obm *OrderBookManager) Subscribe(taskID, symbol string, dataChan chan *OrderBookData) error {
	obm.mutex.Lock()
	defer obm.mutex.Unlock()

	// 初始化交易对的订阅映射
	if obm.subscriptions[symbol] == nil {
		obm.subscriptions[symbol] = make(map[string]chan *OrderBookData)
	}

	// 检查是否已经订阅
	if _, exists := obm.subscriptions[symbol][taskID]; exists {
		log.Printf("⚠️  任务 %s 已订阅交易对 %s", taskID, symbol)
		return nil
	}

	// 添加订阅
	obm.subscriptions[symbol][taskID] = dataChan
	obm.refCounts[symbol]++

	log.Printf("📈 任务 %s 订阅交易对 %s (引用计数: %d)", taskID, symbol, obm.refCounts[symbol])

	// 如果是第一个订阅者，启动WebSocket连接
	if obm.refCounts[symbol] == 1 {
		obm.startWebSocketConnection(symbol)
	}

	// 如果有缓存数据，立即发送
	if latestData, exists := obm.latestData[symbol]; exists {
		select {
		case dataChan <- latestData:
		default:
			// 通道满了，跳过
		}
	}

	return nil
}

// Unsubscribe 取消订阅交易对盘口数据
func (obm *OrderBookManager) Unsubscribe(taskID, symbol string) {
	obm.mutex.Lock()
	defer obm.mutex.Unlock()

	// 检查订阅是否存在
	if obm.subscriptions[symbol] == nil {
		return
	}

	if _, exists := obm.subscriptions[symbol][taskID]; !exists {
		return
	}

	// 删除订阅
	delete(obm.subscriptions[symbol], taskID)
	obm.refCounts[symbol]--

	log.Printf("📉 任务 %s 取消订阅交易对 %s (引用计数: %d)", taskID, symbol, obm.refCounts[symbol])

	// 如果没有订阅者了，停止WebSocket连接
	if obm.refCounts[symbol] <= 0 {
		obm.stopWebSocketConnection(symbol)
		delete(obm.subscriptions, symbol)
		delete(obm.refCounts, symbol)
		delete(obm.latestData, symbol)
	}
}

// startWebSocketConnection 启动交易对的WebSocket连接
func (obm *OrderBookManager) startWebSocketConnection(symbol string) {
	log.Printf("🔌 启动交易对 %s 的WebSocket连接", symbol)

	// 创建停止信号通道
	stopChan := make(chan struct{})
	obm.connections[symbol] = stopChan

	// 创建内部数据通道
	internalChan := make(chan *OrderBookData, 1000)

	// 启动WebSocket连接
	go obm.binanceClient.SubscribeOrderBook(symbol, internalChan, stopChan)

	// 启动数据广播协程
	go obm.broadcastOrderBookData(symbol, internalChan, stopChan)
}

// stopWebSocketConnection 停止交易对的WebSocket连接
func (obm *OrderBookManager) stopWebSocketConnection(symbol string) {
	log.Printf("🔌 停止交易对 %s 的WebSocket连接", symbol)

	if stopChan, exists := obm.connections[symbol]; exists {
		close(stopChan)
		delete(obm.connections, symbol)
	}
}

// broadcastOrderBookData 广播盘口数据给所有订阅者
func (obm *OrderBookManager) broadcastOrderBookData(symbol string, internalChan <-chan *OrderBookData, stopChan <-chan struct{}) {
	for {
		select {
		case <-stopChan:
			log.Printf("📊 交易对 %s 的数据广播已停止", symbol)
			return

		case orderBook := <-internalChan:
			if orderBook == nil {
				continue
			}

			// 更新缓存
			obm.mutex.Lock()
			obm.latestData[symbol] = orderBook

			// 广播给所有订阅者
			if subscribers, exists := obm.subscriptions[symbol]; exists {
				for taskID, dataChan := range subscribers {
					select {
					case dataChan <- orderBook:
						// 成功发送
					default:
						// 通道满了，记录警告但不阻塞
						log.Printf("⚠️  任务 %s 的盘口数据通道已满，跳过本次数据", taskID)
					}
				}
			}
			obm.mutex.Unlock()
		}
	}
}

// GetLatestOrderBook 获取交易对的最新盘口数据
func (obm *OrderBookManager) GetLatestOrderBook(symbol string) (*OrderBookData, bool) {
	obm.mutex.RLock()
	defer obm.mutex.RUnlock()

	data, exists := obm.latestData[symbol]
	return data, exists
}

// GetSubscriptionCount 获取交易对的订阅数量
func (obm *OrderBookManager) GetSubscriptionCount(symbol string) int {
	obm.mutex.RLock()
	defer obm.mutex.RUnlock()

	return obm.refCounts[symbol]
}

// Shutdown 关闭盘口管理器
func (obm *OrderBookManager) Shutdown() {
	obm.mutex.Lock()
	defer obm.mutex.Unlock()

	log.Println("📊 正在关闭全局盘口管理器...")

	// 停止所有WebSocket连接
	for symbol := range obm.connections {
		obm.stopWebSocketConnection(symbol)
	}

	// 清理所有数据
	obm.subscriptions = make(map[string]map[string]chan *OrderBookData)
	obm.refCounts = make(map[string]int)
	obm.latestData = make(map[string]*OrderBookData)
	obm.connections = make(map[string]chan struct{})

	log.Println("📊 全局盘口管理器已关闭")
}
