package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/adshao/go-binance/v2/futures"
	"github.com/gorilla/websocket"
	"github.com/shopspring/decimal"
)

// 自定义错误类型
type OrderCancelledByModificationError struct {
	OrderID int64
	Reason  string
}

func (e *OrderCancelledByModificationError) Error() string {
	return fmt.Sprintf("订单 %d 在修改过程中被交易所取消: %s", e.OrderID, e.Reason)
}

// BinanceClient 币安客户端
type BinanceClient struct {
	client     *futures.Client
	apiKey     string
	secretKey  string
	wsConn     *websocket.Conn
	wsEndpoint string
}

// NewBinanceClient 创建新的币安客户端
func NewBinanceClient() *BinanceClient {
	// 从配置文件获取API密钥
	apiKey, secretKey := GetBinanceConfig()

	// 创建币安期货客户端
	client := binance.NewFuturesClient(apiKey, secretKey)

	log.Println("🔴 使用币安主网 - 请确保你了解风险！")

	return &BinanceClient{
		client:    client,
		apiKey:    apiKey,
		secretKey: secretKey,
	}
}

// GetOrderBook 获取订单簿数据
func (bc *BinanceClient) GetOrderBook(symbol string) (*OrderBookData, error) {
	depth, err := bc.client.NewDepthService().Symbol(symbol).Limit(5).Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取订单簿失败: %v", err)
	}

	if len(depth.Bids) == 0 || len(depth.Asks) == 0 {
		return nil, fmt.Errorf("订单簿数据为空")
	}

	bidPrice, _ := decimal.NewFromString(depth.Bids[0].Price)
	bidQty, _ := decimal.NewFromString(depth.Bids[0].Quantity)
	askPrice, _ := decimal.NewFromString(depth.Asks[0].Price)
	askQty, _ := decimal.NewFromString(depth.Asks[0].Quantity)

	return &OrderBookData{
		Symbol:    symbol,
		BidPrice:  bidPrice,
		BidQty:    bidQty,
		AskPrice:  askPrice,
		AskQty:    askQty,
		Timestamp: time.Now(),
	}, nil
}

// PlaceLimitOrder 下限价单
func (bc *BinanceClient) PlaceLimitOrder(symbol string, side futures.SideType, quantity, price decimal.Decimal, postOnly bool) (*futures.CreateOrderResponse, error) {
	// 根据postOnly参数选择TimeInForce
	var timeInForce futures.TimeInForceType
	if postOnly {
		timeInForce = futures.TimeInForceTypeGTX // GTX: Good Till Crossing (Post Only)
	} else {
		timeInForce = futures.TimeInForceTypeGTC // Good Till Cancel
	}

	order, err := bc.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(futures.OrderTypeLimit).
		TimeInForce(timeInForce).
		Quantity(quantity.String()).
		Price(price.String()).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("下限价单失败: %v", err)
	}

	timeInForceDesc := "GTC"
	if postOnly {
		timeInForceDesc = "POST_ONLY"
	}
	log.Printf("✅ 限价单已下达: %s %s %s @ %s, 订单ID: %d, 模式: %s",
		symbol, side, quantity.String(), price.String(), order.OrderID, timeInForceDesc)

	return order, nil
}

// PlaceLimitOrderWithPriceMatch 使用priceMatch参数下限价单（推荐使用）
func (bc *BinanceClient) PlaceLimitOrderWithPriceMatch(symbol string, side futures.SideType, quantity decimal.Decimal, priceMatch string, postOnly bool) (*futures.CreateOrderResponse, error) {
	// 根据postOnly参数选择TimeInForce
	var timeInForce futures.TimeInForceType
	if postOnly {
		timeInForce = futures.TimeInForceTypeGTX // GTX: Good Till Crossing (Post Only)
	} else {
		timeInForce = futures.TimeInForceTypeGTC // Good Till Cancel
	}

	log.Printf("🔄 开始下单(使用priceMatch): 交易对=%s, 方向=%s, 数量=%s, priceMatch=%s", symbol, side, quantity.String(), priceMatch)

	// 创建下单服务
	service := bc.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(futures.OrderTypeLimit).
		TimeInForce(timeInForce).
		Quantity(quantity.String())

	// 尝试使用反射或者直接设置priceMatch参数
	// 由于当前版本的go-binance可能不支持PriceMatch方法，我们需要使用其他方式
	// 暂时回退到使用盘口价格的方式，但保留接口以便将来升级

	// 获取当前盘口价格作为临时解决方案
	orderBook, err := bc.GetOrderBook(symbol)
	if err != nil {
		return nil, fmt.Errorf("获取盘口价格失败: %v", err)
	}

	var limitPrice decimal.Decimal
	if side == futures.SideTypeBuy {
		// 买单：使用买一价，确保成为Maker
		limitPrice = orderBook.BidPrice
	} else {
		// 卖单：使用卖一价，确保成为Maker
		limitPrice = orderBook.AskPrice
	}

	log.Printf("🛡️ 使用盘口价格作为限价: %s (priceMatch=%s)", limitPrice.String(), priceMatch)

	// 设置价格并执行下单
	order, err := service.Price(limitPrice.String()).Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("下限价单失败: %v", err)
	}

	timeInForceDesc := "GTC"
	if postOnly {
		timeInForceDesc = "POST_ONLY"
	}
	log.Printf("✅ 限价单已下达(模拟priceMatch): %s %s %s @ %s, 订单ID: %d, 模式: %s",
		symbol, side, quantity.String(), limitPrice.String(), order.OrderID, timeInForceDesc)

	return order, nil
}

// PlaceMarketOrder 下市价单
func (bc *BinanceClient) PlaceMarketOrder(symbol string, side futures.SideType, quantity decimal.Decimal) (*futures.CreateOrderResponse, error) {
	order, err := bc.client.NewCreateOrderService().
		Symbol(symbol).
		Side(side).
		Type(futures.OrderTypeMarket).
		Quantity(quantity.String()).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("下市价单失败: %v", err)
	}

	log.Printf("✅ 市价单已下达: %s %s %s, 订单ID: %d",
		symbol, side, quantity.String(), order.OrderID)

	return order, nil
}

// CancelOrder 撤销订单
func (bc *BinanceClient) CancelOrder(symbol string, orderID int64) error {
	_, err := bc.client.NewCancelOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return fmt.Errorf("撤销订单失败: %v", err)
	}

	log.Printf("✅ 订单已撤销: %s, 订单ID: %d", symbol, orderID)
	return nil
}

// ModifyOrder 修改订单（使用币安原生修改订单API）
func (bc *BinanceClient) ModifyOrder(symbol string, orderID int64, newPrice decimal.Decimal, quantity decimal.Decimal, side futures.SideType, postOnly bool) (*futures.ModifyOrderResponse, error) {
	if bc.client == nil {
		return nil, fmt.Errorf("币安客户端未初始化")
	}

	log.Printf("🔄 开始修改订单: 订单ID=%d, 新价格=%s, 数量=%s", orderID, newPrice.String(), quantity.String())

	// 使用币安原生修改订单API
	order, err := bc.client.NewModifyOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Side(side).
		Quantity(quantity.String()).
		Price(newPrice.String()).
		Do(context.Background())

	if err != nil {
		errStr := err.Error()

		// 检查是否是-5027错误（无需修改订单）
		if strings.Contains(errStr, "-5027") || strings.Contains(errStr, "No need to modify the order") {
			log.Printf("ℹ️ 订单 %d 无需修改，价格已经是最优价格", orderID)
			// 对于-5027错误，我们返回一个特殊的成功响应，表示订单无需修改
			return &futures.ModifyOrderResponse{
				OrderID: orderID,
				Price:   newPrice.String(), // 保持原价格
			}, nil
		}

		// 检查是否是Post-Only相关的取消错误
		if strings.Contains(errStr, "-5022") || strings.Contains(errStr, "Post-only order will be canceled") {
			log.Printf("⚠️ 订单 %d 因Post-Only规则被交易所取消", orderID)
			return nil, &OrderCancelledByModificationError{
				OrderID: orderID,
				Reason:  "Post-Only order will be canceled",
			}
		}

		// 检查其他可能导致订单取消的错误
		if strings.Contains(errStr, "will be canceled") || strings.Contains(errStr, "canceled") {
			log.Printf("⚠️ 订单 %d 在修改过程中被交易所取消: %v", orderID, err)
			return nil, &OrderCancelledByModificationError{
				OrderID: orderID,
				Reason:  errStr,
			}
		}

		return nil, fmt.Errorf("修改订单失败: %v", err)
	}

	log.Printf("✅ 订单修改成功: %s, 订单ID: %d, 新价格: %s, 数量: %s",
		symbol, order.OrderID, newPrice.String(), quantity.String())

	return order, nil
}

// ModifyOrderWithPriceMatch 使用priceMatch参数修改订单（推荐使用）
func (bc *BinanceClient) ModifyOrderWithPriceMatch(symbol string, orderID int64, quantity decimal.Decimal, side futures.SideType, priceMatch string) (*futures.ModifyOrderResponse, error) {
	if bc.client == nil {
		return nil, fmt.Errorf("币安客户端未初始化")
	}

	log.Printf("🔄 开始修改订单(使用priceMatch): 订单ID=%d, 数量=%s, priceMatch=%s", orderID, quantity.String(), priceMatch)

	// 使用币安原生修改订单API，使用priceMatch参数而不是固定价格
	service := bc.client.NewModifyOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Side(side).
		Quantity(quantity.String()).
		PriceMatch(futures.PriceMatchType(priceMatch)) // 使用priceMatch参数

	// 注意：当使用priceMatch时，不能同时设置Price参数

	order, err := service.Do(context.Background())

	if err != nil {
		errStr := err.Error()

		// 检查是否是-5027错误（无需修改订单）
		if strings.Contains(errStr, "-5027") || strings.Contains(errStr, "No need to modify the order") {
			log.Printf("ℹ️ 订单 %d 无需修改，价格已经是最优价格", orderID)
			// 对于-5027错误，我们返回一个特殊的成功响应，表示订单无需修改
			return &futures.ModifyOrderResponse{
				OrderID: orderID,
				Price:   "", // 价格保持不变
			}, nil
		}

		// 检查是否是Post-Only相关的取消错误
		if strings.Contains(errStr, "-5022") || strings.Contains(errStr, "Post-only order will be canceled") {
			log.Printf("⚠️ 订单 %d 因Post-Only规则被交易所取消", orderID)
			return nil, &OrderCancelledByModificationError{
				OrderID: orderID,
				Reason:  "Post-Only order will be canceled",
			}
		}

		// 检查其他可能导致订单取消的错误
		if strings.Contains(errStr, "will be canceled") || strings.Contains(errStr, "canceled") {
			log.Printf("⚠️ 订单 %d 在修改过程中被交易所取消: %v", orderID, err)
			return nil, &OrderCancelledByModificationError{
				OrderID: orderID,
				Reason:  errStr,
			}
		}

		return nil, fmt.Errorf("修改订单失败: %v", err)
	}

	log.Printf("✅ 订单修改成功(priceMatch): %s, 订单ID: %d, 数量: %s, priceMatch: %s",
		symbol, order.OrderID, quantity.String(), priceMatch)

	return order, nil
}

// PriceMatch 常量定义
const (
	PriceMatchQueue      = "QUEUE"       // 排队价格
	PriceMatchQueue5     = "QUEUE_5"     // 排队价格+5基点
	PriceMatchQueue10    = "QUEUE_10"    // 排队价格+10基点
	PriceMatchQueue20    = "QUEUE_20"    // 排队价格+20基点
	PriceMatchOpponent   = "OPPONENT"    // 对手价格
	PriceMatchOpponent5  = "OPPONENT_5"  // 对手价格+5基点
	PriceMatchOpponent10 = "OPPONENT_10" // 对手价格+10基点
	PriceMatchOpponent20 = "OPPONENT_20" // 对手价格+20基点
)

// GetOrderStatus 获取订单状态
func (bc *BinanceClient) GetOrderStatus(symbol string, orderID int64) (*futures.Order, error) {
	order, err := bc.client.NewGetOrderService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取订单状态失败: %v", err)
	}

	return order, nil
}

// GetPosition 获取仓位信息
func (bc *BinanceClient) GetPosition(symbol string) (*futures.PositionRisk, error) {
	positions, err := bc.client.NewGetPositionRiskService().
		Symbol(symbol).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取仓位失败: %v", err)
	}

	if len(positions) == 0 {
		return nil, fmt.Errorf("未找到仓位信息")
	}

	return positions[0], nil
}

// GetAccountInfo 获取账户信息
func (bc *BinanceClient) GetAccountInfo() (*futures.Account, error) {
	account, err := bc.client.NewGetAccountService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}

	return account, nil
}

// GetUserTrades 获取用户成交历史
func (bc *BinanceClient) GetUserTrades(symbol string, orderID int64) ([]*futures.AccountTrade, error) {
	trades, err := bc.client.NewListAccountTradeService().
		Symbol(symbol).
		OrderID(orderID).
		Do(context.Background())

	if err != nil {
		return nil, fmt.Errorf("获取成交历史失败: %v", err)
	}

	return trades, nil
}

// SubscribeOrderBook 订阅实时订单簿数据
func (bc *BinanceClient) SubscribeOrderBook(symbol string, orderBookChan chan<- *OrderBookData, stopChan <-chan struct{}) {
	wsEndpoint := "wss://fstream.binance.com/ws/"

	// 币安WebSocket要求交易对名称为小写
	lowerSymbol := strings.ToLower(symbol)
	streamName := fmt.Sprintf("%s@depth5@100ms", lowerSymbol)
	wsURL := wsEndpoint + streamName

	for {
		select {
		case <-stopChan:
			if bc.wsConn != nil {
				bc.wsConn.Close()
			}
			return
		default:
			err := bc.connectWebSocket(wsURL, orderBookChan, stopChan)
			if err != nil {
				log.Printf("❌ WebSocket连接失败: %v, 5秒后重试...", err)
				time.Sleep(5 * time.Second)
			}
		}
	}
}

// connectWebSocket 连接WebSocket
func (bc *BinanceClient) connectWebSocket(wsURL string, orderBookChan chan<- *OrderBookData, stopChan <-chan struct{}) error {
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		return err
	}
	defer conn.Close()

	bc.wsConn = conn

	channelFullCount := 0 // 记录通道满的次数

	for {
		select {
		case <-stopChan:
			return nil
		default:
			var msg map[string]interface{}
			err := conn.ReadJSON(&msg)
			if err != nil {
				return fmt.Errorf("读取WebSocket数据失败: %v", err)
			}

			// 解析订单簿数据
			orderBook := bc.parseOrderBookMessage(msg)
			if orderBook != nil {
				select {
				case orderBookChan <- orderBook:
					// 成功发送，重置计数器
					channelFullCount = 0
				default:
					// 通道满了，增加计数器
					channelFullCount++
					// 每100次才输出一次警告，避免日志过多
					if channelFullCount%100 == 1 {
						log.Printf("⚠️ 订单簿通道满，已跳过 %d 次数据", channelFullCount)
					}
				}
			}
		}
	}
}

// parseOrderBookMessage 解析订单簿消息
func (bc *BinanceClient) parseOrderBookMessage(msg map[string]interface{}) *OrderBookData {
	bids, ok := msg["b"].([]interface{})
	if !ok || len(bids) == 0 {
		return nil
	}

	asks, ok := msg["a"].([]interface{})
	if !ok || len(asks) == 0 {
		return nil
	}

	// 解析买一价和买一量
	bidData := bids[0].([]interface{})
	bidPrice, _ := decimal.NewFromString(bidData[0].(string))
	bidQty, _ := decimal.NewFromString(bidData[1].(string))

	// 解析卖一价和卖一量
	askData := asks[0].([]interface{})
	askPrice, _ := decimal.NewFromString(askData[0].(string))
	askQty, _ := decimal.NewFromString(askData[1].(string))

	symbol, _ := msg["s"].(string)

	return &OrderBookData{
		Symbol:    symbol,
		BidPrice:  bidPrice,
		BidQty:    bidQty,
		AskPrice:  askPrice,
		AskQty:    askQty,
		Timestamp: time.Now(),
	}
}

// ValidateAPIConnection 验证API连接
func (bc *BinanceClient) ValidateAPIConnection() error {
	log.Println("🔍 验证币安API连接...")

	// 添加重试机制
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			log.Printf("第 %d 次重试...", i+1)
			time.Sleep(time.Duration(i) * 2 * time.Second) // 递增延迟
		}

		// 测试获取账户信息
		account, err := bc.GetAccountInfo()
		if err != nil {
			log.Printf("API连接尝试 %d 失败: %v", i+1, err)
			if i == maxRetries-1 {
				return fmt.Errorf("API连接验证失败: %v", err)
			}
			continue
		}

		log.Printf("✅ API连接成功!")
		log.Printf("账户总余额: %s USDT", account.TotalWalletBalance)
		log.Printf("可用余额: %s USDT", account.AvailableBalance)
		return nil
	}

	return fmt.Errorf("API连接验证失败: 重试 %d 次后仍然失败", maxRetries)
}

// GetSymbolInfo 获取交易对信息
func (bc *BinanceClient) GetSymbolInfo(symbol string) (*futures.Symbol, error) {
	exchangeInfo, err := bc.client.NewExchangeInfoService().Do(context.Background())
	if err != nil {
		return nil, fmt.Errorf("获取交易所信息失败: %v", err)
	}

	for _, s := range exchangeInfo.Symbols {
		if s.Symbol == symbol {
			return &s, nil
		}
	}

	return nil, fmt.Errorf("未找到交易对: %s", symbol)
}

// FormatQuantity 根据交易对精度格式化数量
func (bc *BinanceClient) FormatQuantity(symbol string, quantity decimal.Decimal) (decimal.Decimal, error) {
	symbolInfo, err := bc.GetSymbolInfo(symbol)
	if err != nil {
		return decimal.Zero, err
	}

	// 找到LOT_SIZE过滤器
	for _, filter := range symbolInfo.Filters {
		if filter["filterType"] == "LOT_SIZE" {
			stepSize, _ := decimal.NewFromString(filter["stepSize"].(string))

			// 根据stepSize调整数量精度
			if stepSize.GreaterThan(decimal.Zero) {
				return quantity.Div(stepSize).Floor().Mul(stepSize), nil
			}
		}
	}

	return quantity, nil
}

// FormatPrice 根据交易对精度格式化价格
func (bc *BinanceClient) FormatPrice(symbol string, price decimal.Decimal) (decimal.Decimal, error) {
	symbolInfo, err := bc.GetSymbolInfo(symbol)
	if err != nil {
		return decimal.Zero, err
	}

	// 找到PRICE_FILTER过滤器
	for _, filter := range symbolInfo.Filters {
		if filter["filterType"] == "PRICE_FILTER" {
			tickSize, _ := decimal.NewFromString(filter["tickSize"].(string))

			// 根据tickSize调整价格精度
			if tickSize.GreaterThan(decimal.Zero) {
				return price.Div(tickSize).Floor().Mul(tickSize), nil
			}
		}
	}

	return price, nil
}
