package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v2"
)

// Config 配置结构
type Config struct {
	Binance struct {
		APIKey    string `yaml:"api_key"`
		SecretKey string `yaml:"secret_key"`
	} `yaml:"binance"`
	Trading struct {
		MakerFeeRate float64 `yaml:"maker_fee_rate"` // 挂单费率
		TakerFeeRate float64 `yaml:"taker_fee_rate"` // 吃单费率
	} `yaml:"trading"`
	Server struct {
		Port int `yaml:"port"`
	} `yaml:"server"`
	Logging struct {
		Level string `yaml:"level"`
	} `yaml:"logging"`
}

// 全局配置变量
var AppConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) error {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析YAML
	config := &Config{}
	err = yaml.Unmarshal(data, config)
	if err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证必要的配置项
	if config.Binance.APIKey == "" || config.Binance.APIKey == "你的API密钥" {
		return fmt.Errorf("请在配置文件中设置有效的 binance.api_key")
	}

	if config.Binance.SecretKey == "" || config.Binance.SecretKey == "你的密钥" {
		return fmt.Errorf("请在配置文件中设置有效的 binance.secret_key")
	}

	// 设置默认值
	if config.Server.Port == 0 {
		config.Server.Port = 5879
	}

	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}

	// 设置手续费率默认值
	if config.Trading.MakerFeeRate == 0 {
		config.Trading.MakerFeeRate = 0.0002 // 默认挂单费率 0.02%
	}

	if config.Trading.TakerFeeRate == 0 {
		config.Trading.TakerFeeRate = 0.0004 // 默认吃单费率 0.04%
	}

	AppConfig = config
	log.Printf("✅ 配置文件加载成功: %s", configPath)
	log.Printf("🔧 API密钥: %s...", config.Binance.APIKey[:8])
	log.Printf("🔴 使用币安主网 - 请确保你了解风险！")

	return nil
}

// LoadEmbeddedConfig 加载内嵌配置
func LoadEmbeddedConfig() error {
	// 获取内嵌配置
	embeddedConfig := GetEmbeddedConfig()

	// 转换为原有的Config结构
	config := embeddedConfig.ConvertToConfig()

	// 验证必要的配置项
	if config.Binance.APIKey == "" {
		return fmt.Errorf("内嵌配置中的 API Key 不能为空")
	}

	if config.Binance.SecretKey == "" {
		return fmt.Errorf("内嵌配置中的 Secret Key 不能为空")
	}

	// 设置默认值
	if config.Server.Port == 0 {
		config.Server.Port = 5879
	}

	if config.Logging.Level == "" {
		config.Logging.Level = "info"
	}

	// 设置手续费率默认值
	if config.Trading.MakerFeeRate == 0 {
		config.Trading.MakerFeeRate = 0.0002 // 默认挂单费率 0.02%
	}

	if config.Trading.TakerFeeRate == 0 {
		config.Trading.TakerFeeRate = 0.0004 // 默认吃单费率 0.04%
	}

	AppConfig = config
	log.Printf("✅ 内嵌配置加载成功")
	log.Printf("🔧 API密钥: %s...", config.Binance.APIKey[:8])
	log.Printf("🔴 使用币安主网 - 请确保你了解风险！")

	return nil
}

// GetBinanceConfig 获取币安配置
func GetBinanceConfig() (apiKey, secretKey string) {
	if AppConfig == nil {
		log.Fatal("配置未加载，请先调用 LoadConfig")
	}
	return AppConfig.Binance.APIKey, AppConfig.Binance.SecretKey
}

// GetTradingConfig 获取交易配置
func GetTradingConfig() (makerFeeRate, takerFeeRate float64) {
	if AppConfig == nil {
		log.Fatal("配置未加载，请先调用 LoadConfig")
	}
	return AppConfig.Trading.MakerFeeRate, AppConfig.Trading.TakerFeeRate
}
