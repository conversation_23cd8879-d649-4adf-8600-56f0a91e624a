package main

import (
	"sync"
	"time"

	"github.com/shopspring/decimal"
)

// HedgeTask 对冲任务结构体
type HedgeTask struct {
	ID                string          `json:"id"`                 // 任务唯一标识
	Symbol            string          `json:"symbol"`             // 交易对 (ETHUSDC/ETHUSDT)
	Direction         string          `json:"direction"`          // 对冲方向 (long/short)
	TargetPrice       decimal.Decimal `json:"target_price"`       // 目标价格 T
	StopRate          decimal.Decimal `json:"stop_rate"`          // 对冲阈值 (如 0.002 表示 0.2%)
	Amount            decimal.Decimal `json:"amount"`             // 下单数量（已废弃，保留兼容性）
	SlippageTolerance decimal.Decimal `json:"slippage_tolerance"` // 动态对冲滑点容忍度
	StopSlippage      decimal.Decimal `json:"stop_slippage"`      // 调仓滑点容忍度
	MarketTimeout     int             `json:"market_timeout"`     // 市价成交检测时间阈值(秒)
	VolumeThreshold   decimal.Decimal `json:"volume_threshold"`   // 盘口量阈值
	PostOnlyMode      bool            `json:"post_only_mode"`     // 是否只允许限价成交（POST_ONLY模式）
	Source            string          `json:"source"`             // 来源标识 (manual/options)
	OptionContract    string          `json:"option_contract"`    // 期权合约名称
	Status            string          `json:"status"`             // 任务状态 (running/stopped/error)

	// 三仓位状态管理
	ConfiguredPosition *ConfiguredPosition `json:"configured_position"` // 设定仓位
	RequiredPosition   *RequiredPosition   `json:"required_position"`   // 应有仓位
	CurrentPosition    *CurrentPosition    `json:"current_position"`    // 现有仓位

	// 订单监控状态
	CurrentOrderID        string          `json:"current_order_id"`        // 当前订单ID
	CurrentOrderPrice     decimal.Decimal `json:"current_order_price"`     // 当前挂单价格
	CurrentOrderSide      string          `json:"current_order_side"`      // 当前订单方向(buy/sell)
	OrderStartTime        time.Time       `json:"order_start_time"`        // 订单开始时间
	IsOrderInProgress     bool            `json:"is_order_in_progress"`    // 是否正在下单（任务锁）
	IsMonitoringOrderBook bool            `json:"is_monitoring_orderbook"` // 是否正在监控盘口（已废弃，保留兼容性）
	OrderModifyCount      int             `json:"order_modify_count"`      // 当前订单修改次数

	// 订单执行器私有通道（重构新增）
	privateOrderBookChan chan *OrderBookData `json:"-"` // 订单执行器专用的盘口数据通道（不序列化）

	Events          []Event       `json:"events"`     // 事件记录
	Statistics      *Stats        `json:"statistics"` // 统计信息
	StopChan        chan struct{} `json:"-"`          // 停止信号 (不序列化)
	CreatedAt       time.Time     `json:"created_at"` // 创建时间
	UpdatedAt       time.Time     `json:"updated_at"` // 最后更新时间
	mutex           sync.RWMutex  `json:"-"`          // 读写锁保护任务数据 (不序列化)
	onDataChange    func()        `json:"-"`          // 数据变化回调函数 (不序列化)
	skipCloseOnStop bool          `json:"-"`          // 停止时跳过平仓 (不序列化)
}

// ConfiguredPosition 设定仓位信息
type ConfiguredPosition struct {
	Side           string          `json:"side"`             // 设定仓位方向 (long/short/none)
	Size           decimal.Decimal `json:"size"`             // 设定仓位大小
	LastUpdateTime time.Time       `json:"last_update_time"` // 最后更新时间
	UpdateSource   string          `json:"update_source"`    // 更新来源 (initial/api_adjustment)
}

// RequiredPosition 应有仓位信息（根据市价和阈值计算）
type RequiredPosition struct {
	Side              string          `json:"side"`                // 应有仓位方向 (long/short/none)
	Size              decimal.Decimal `json:"size"`                // 应有仓位大小
	LastCalculateTime time.Time       `json:"last_calculate_time"` // 最后计算时间
	CalculatePrice    decimal.Decimal `json:"calculate_price"`     // 计算时使用的市价
	TriggerSource     string          `json:"trigger_source"`      // 触发源
}

// CurrentPosition 现有仓位信息（实际在交易所的仓位）
type CurrentPosition struct {
	Side string          `json:"side"` // 现有仓位方向 (long/short/none)
	Size decimal.Decimal `json:"size"` // 现有仓位大小
}

// Event 事件记录
type Event struct {
	ID               string          `json:"id"`                // 事件ID
	Type             string          `json:"type"`              // 事件类型 (open/close)
	OrderType        string          `json:"order_type"`        // 订单类型 (limit/market)
	ExecutionType    string          `json:"execution_type"`    // 实际成交方式 (maker/taker)
	TheoreticalPrice decimal.Decimal `json:"theoretical_price"` // 理论成交价
	ActualPrice      decimal.Decimal `json:"actual_price"`      // 实际成交价
	PriceLoss        decimal.Decimal `json:"price_loss"`        // 价格损耗 (可为负)
	PriceLossRatio   decimal.Decimal `json:"price_loss_ratio"`  // 价格损耗比例
	Fee              decimal.Decimal `json:"fee"`               // 手续费
	FeeRatio         decimal.Decimal `json:"fee_ratio"`         // 手续费比例
	OrderID          string          `json:"order_id"`          // 成交订单ID
	Timestamp        time.Time       `json:"timestamp"`         // 事件时间
}

// Stats 统计信息
type Stats struct {
	TotalTrades       int             `json:"total_trades"`         // 总交易次数
	TotalPnL          decimal.Decimal `json:"total_pnl"`            // 总盈亏
	TotalFees         decimal.Decimal `json:"total_fees"`           // 总手续费
	TotalPriceLoss    decimal.Decimal `json:"total_price_loss"`     // 总价格损耗
	WinRate           decimal.Decimal `json:"win_rate"`             // 胜率
	AvgPriceLossRatio decimal.Decimal `json:"avg_price_loss_ratio"` // 平均价格损耗比例
	AvgFeeRatio       decimal.Decimal `json:"avg_fee_ratio"`        // 平均手续费比例
}

// OrderBookData 盘口数据
type OrderBookData struct {
	Symbol    string          // 交易对
	BidPrice  decimal.Decimal // 买一价
	BidQty    decimal.Decimal // 买一量
	AskPrice  decimal.Decimal // 卖一价
	AskQty    decimal.Decimal // 卖一量
	Timestamp time.Time       // 时间戳
}

// TaskRequest 创建任务请求
type TaskRequest struct {
	Symbol            string  `json:"symbol"`
	Direction         string  `json:"direction"`
	TargetPrice       float64 `json:"target_price"`
	StopRate          float64 `json:"stop_rate"`
	Amount            float64 `json:"amount"`
	SlippageTolerance float64 `json:"slippage_tolerance"`
	StopSlippage      float64 `json:"stop_slippage"`
	MarketTimeout     int     `json:"market_timeout"`
	VolumeThreshold   float64 `json:"volume_threshold"`
	PostOnlyMode      bool    `json:"post_only_mode"`
	Source            string  `json:"source,omitempty"`          // 来源标识 (manual/options)
	OptionContract    string  `json:"option_contract,omitempty"` // 期权合约名称
}

// TaskResponse 任务响应
type TaskResponse struct {
	TaskID  string `json:"task_id"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// StopTaskRequest 停止任务请求
type StopTaskRequest struct {
	TaskID string `json:"task_id"`
}

// UpdateTaskAmountRequest 更新任务数量请求
type UpdateTaskAmountRequest struct {
	Amount         float64 `json:"amount"`                    // 新的总数量
	UpdateType     string  `json:"update_type"`               // 更新类型 (sell_open/buy_close/exercise)
	Source         string  `json:"source,omitempty"`          // 来源标识
	OptionContract string  `json:"option_contract,omitempty"` // 期权合约名称
}

// UpdateTaskAmountResponse 更新任务数量响应
type UpdateTaskAmountResponse struct {
	Success   bool    `json:"success"`
	TaskID    string  `json:"task_id"`
	OldAmount float64 `json:"old_amount"`
	NewAmount float64 `json:"new_amount"`
	Message   string  `json:"message"`
}

// QueryTaskByOptionResponse 根据期权合约查询任务响应
type QueryTaskByOptionResponse struct {
	Success bool   `json:"success"`
	TaskID  string `json:"task_id,omitempty"`
	Exists  bool   `json:"exists"`
}

// TaskManager 任务管理器
type TaskManager struct {
	tasks       map[string]*HedgeTask
	mutex       sync.RWMutex
	dataManager *DataManager
	eventLogger *EventLogger
}

// 常量定义
const (
	// 任务状态
	StatusRunning = "running"
	StatusStopped = "stopped"
	StatusError   = "error"

	// 对冲方向
	DirectionLong  = "long"
	DirectionShort = "short"

	// 仓位方向
	SideLong  = "long"
	SideShort = "short"
	SideNone  = "none" // 无仓位

	// 事件类型
	EventTypeOpen  = "open"
	EventTypeClose = "close"

	// 订单类型
	OrderTypeLimit     = "limit"
	OrderTypeMarket    = "market"
	OrderTypeCancelled = "cancelled"
	OrderTypeFailed    = "failed"

	// 成交方式
	ExecutionTypeMaker   = "maker"
	ExecutionTypeTaker   = "taker"
	ExecutionTypeUnknown = "unknown"

	// 触发源类型
	TriggerSourcePriceChange   = "price_trigger"  // 价格触发（正常开平仓）
	TriggerSourceAPIAdjustment = "api_adjustment" // API调仓
	TriggerSourceStopClose     = "stop_close"     // 停止平仓

	// 更新来源类型
	UpdateSourceInitial       = "initial"        // 初始设置
	UpdateSourceAPIAdjustment = "api_adjustment" // API调整

	// 交易对
	SymbolETHUSDC = "ETHUSDC"
	SymbolETHUSDT = "ETHUSDT"
)
